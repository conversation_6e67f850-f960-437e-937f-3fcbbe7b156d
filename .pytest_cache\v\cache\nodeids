["tests/test_vehicle_detector.py::TestVehicleDetector::test_detect_vehicles_empty_result", "tests/test_vehicle_detector.py::TestVehicleDetector::test_detect_vehicles_filter_non_vehicles", "tests/test_vehicle_detector.py::TestVehicleDetector::test_detect_vehicles_with_detections", "tests/test_vehicle_detector.py::TestVehicleDetector::test_detection_error_handling", "tests/test_vehicle_detector.py::TestVehicleDetector::test_detector_initialization", "tests/test_vehicle_detector.py::TestVehicleDetector::test_draw_detections", "tests/test_vehicle_detector.py::TestVehicleDetector::test_get_statistics", "tests/test_vehicle_detector.py::TestVehicleDetector::test_model_loading_error", "tests/test_vehicle_detector.py::TestVehicleDetector::test_reset_statistics", "tests/test_vehicle_detector.py::TestVehicleDetector::test_update_thresholds", "tests/test_vehicle_detector.py::TestVehicleDetector::test_vehicle_classes"]