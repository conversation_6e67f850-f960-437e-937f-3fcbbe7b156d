# Real-time Traffic Surveillance and Detection System

A comprehensive computer vision and deep learning system for real-time traffic monitoring, vehicle detection, license plate recognition, and traffic analytics.

## Features

### Core Detection Capabilities
- **Vehicle Detection**: Real-time detection of cars, trucks, buses, motorcycles using YOLOv8
- **License Plate Recognition**: Automatic license plate detection and OCR
- **Traffic Sign Recognition**: Detection and classification of traffic signs
- **Pedestrian Detection**: Person detection for safety monitoring

### Traffic Analytics
- **Vehicle Counting**: Bidirectional vehicle counting with entry/exit lines
- **Speed Estimation**: Real-time speed calculation using computer vision
- **Traffic Flow Analysis**: Congestion detection and flow metrics
- **Violation Detection**: Speed violations, red light violations, wrong-way detection

### Real-time Processing
- **Multi-source Support**: Camera feeds, video files, RTSP streams
- **GPU Acceleration**: CUDA support for high-performance inference
- **Optimized Pipeline**: Frame skipping and buffer management for real-time processing
- **Multi-threading**: Parallel processing for multiple video sources

### Web Dashboard
- **Live Video Feeds**: Real-time video streaming with detection overlays
- **Analytics Dashboard**: Charts and metrics for traffic analysis
- **Alert System**: Real-time notifications for violations and incidents
- **Data Export**: Export detection results and analytics data

## Installation

### Prerequisites
- Python 3.8 or higher
- CUDA-compatible GPU (optional, for acceleration)
- Webcam or IP camera (for live monitoring)

### Setup
1. Clone the repository:
```bash
git clone <repository-url>
cd traffic-surveillance-system
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Download pre-trained models:
```bash
# YOLOv8 models will be downloaded automatically on first run
# For custom models, place them in the models/ directory
```

## Configuration

Edit `config.yaml` to customize the system:

- **Video Sources**: Configure camera inputs, video files, or RTSP streams
- **Model Settings**: Adjust confidence thresholds and model paths
- **Analytics**: Configure counting lines, speed limits, and violation detection
- **Dashboard**: Set web interface host and port
- **Database**: Configure data storage settings

## Usage

### Basic Usage
```bash
# Run complete system (surveillance + web dashboard)
python main.py

# Run surveillance only
python main.py --mode surveillance

# Run web dashboard only
python main.py --mode dashboard

# Use specific video source
python main.py --video path/to/video.mp4
python main.py --video 0  # Use webcam
python main.py --video rtsp://camera-ip/stream

# Debug mode
python main.py --debug

# Headless mode (no video display)
python main.py --no-display
```

### Advanced Usage
```bash
# Custom configuration file
python main.py --config custom_config.yaml

# Specify output directory
python main.py --output results/

# Multiple options
python main.py --video camera.mp4 --output results/ --debug
```

## Project Structure

```
traffic-surveillance-system/
├── main.py                 # Main application entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── README.md              # This file
├── src/                   # Source code
│   ├── core/              # Core surveillance system
│   ├── models/            # Detection models
│   ├── analytics/         # Traffic analytics
│   ├── web/               # Web dashboard
│   └── utils/             # Utility functions
├── models/                # Pre-trained model files
├── data/                  # Database and data storage
├── logs/                  # Log files
├── static/                # Web assets (CSS, JS, images)
├── templates/             # HTML templates
└── tests/                 # Unit tests
```

## Web Dashboard

Access the web dashboard at `http://localhost:5000` (default) to view:

- Live video feeds with detection overlays
- Real-time traffic statistics
- Vehicle counting and speed monitoring
- Violation alerts and history
- Analytics charts and reports

## API Endpoints

The system provides REST API endpoints for integration:

- `GET /api/stats` - Current traffic statistics
- `GET /api/detections` - Recent detections
- `GET /api/violations` - Traffic violations
- `GET /api/analytics` - Historical analytics data

## Performance Optimization

### GPU Acceleration
- Install CUDA and cuDNN for GPU acceleration
- Set `device: "cuda"` in config.yaml
- Use appropriate model sizes (yolov8n for speed, yolov8m for accuracy)

### CPU Optimization
- Adjust `skip_frames` to process fewer frames
- Reduce video resolution if needed
- Use lighter models (YOLOv8n instead of YOLOv8m)

## Troubleshooting

### Common Issues
1. **CUDA out of memory**: Reduce batch size or use smaller model
2. **Low FPS**: Increase frame skipping or reduce resolution
3. **Model not found**: Ensure model files are in the models/ directory
4. **Camera not detected**: Check camera index and permissions

### Logging
Check `logs/traffic_surveillance.log` for detailed error messages and system status.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- YOLOv8 by Ultralytics
- OpenCV community
- PyTorch team
- Flask framework
