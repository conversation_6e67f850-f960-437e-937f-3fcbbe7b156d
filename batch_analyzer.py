#!/usr/bin/env python3
"""
Batch Image Vehicle Analysis
Process multiple images and generate summary reports
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
import json
import csv
from typing import Dict, List, Any
import time

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from image_analyzer import ImageVehicleAnalyzer


class BatchImageAnalyzer:
    """
    Batch processing for multiple images
    """
    
    def __init__(self, device: str = "cpu"):
        """
        Initialize batch analyzer
        
        Args:
            device: Device to use for inference
        """
        self.analyzer = ImageVehicleAnalyzer(device=device)
        self.results = []
    
    def process_directory(self, input_dir: str, output_dir: str = None, 
                         image_extensions: List[str] = None) -> List[Dict[str, Any]]:
        """
        Process all images in a directory
        
        Args:
            input_dir: Directory containing images
            output_dir: Directory to save results
            image_extensions: List of image file extensions to process
            
        Returns:
            List of analysis results
        """
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        
        input_path = Path(input_dir)
        if not input_path.exists():
            raise ValueError(f"Input directory does not exist: {input_dir}")
        
        # Create output directory
        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
        
        # Find all image files
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"⚠️  No image files found in {input_dir}")
            return []
        
        print(f"📁 Found {len(image_files)} images to process")
        
        # Process each image
        results = []
        start_time = time.time()
        
        for i, image_file in enumerate(image_files, 1):
            print(f"\n📸 Processing {i}/{len(image_files)}: {image_file.name}")
            
            try:
                # Set output path
                if output_dir:
                    output_file = output_path / f"{image_file.stem}_analyzed{image_file.suffix}"
                else:
                    output_file = None
                
                # Analyze image
                result = self.analyzer.analyze_image(
                    str(image_file),
                    output_path=str(output_file) if output_file else None,
                    show_image=False
                )
                
                # Add metadata
                result['metadata'] = {
                    'filename': image_file.name,
                    'filepath': str(image_file),
                    'processed_at': time.time()
                }
                
                results.append(result)
                
                # Print quick summary
                summary = result['summary']
                print(f"   ✅ Vehicles: {summary['total_vehicles']}, "
                      f"License Plates: {summary['readable_license_plates']}")
                
            except Exception as e:
                print(f"   ❌ Error processing {image_file.name}: {str(e)}")
                continue
        
        elapsed_time = time.time() - start_time
        print(f"\n⏱️  Batch processing completed in {elapsed_time:.2f} seconds")
        print(f"📊 Successfully processed {len(results)}/{len(image_files)} images")
        
        self.results = results
        return results
    
    def generate_summary_report(self, results: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate summary report from batch results
        
        Args:
            results: List of analysis results (uses self.results if None)
            
        Returns:
            Summary report dictionary
        """
        if results is None:
            results = self.results
        
        if not results:
            return {'error': 'No results to summarize'}
        
        # Initialize counters
        total_images = len(results)
        total_vehicles = 0
        total_license_plates = 0
        readable_license_plates = 0
        vehicle_type_counts = {}
        all_license_plates = []
        
        # Process each result
        for result in results:
            summary = result['summary']
            total_vehicles += summary['total_vehicles']
            total_license_plates += summary['total_license_plates_detected']
            readable_license_plates += summary['readable_license_plates']
            
            # Count vehicle types
            for vehicle_type, count in result['vehicle_breakdown'].items():
                vehicle_type_counts[vehicle_type] = vehicle_type_counts.get(vehicle_type, 0) + count
            
            # Collect license plates
            for plate in result['license_plates']:
                all_license_plates.append({
                    'text': plate['text'],
                    'confidence': plate['confidence'],
                    'image': result['metadata']['filename']
                })
        
        # Calculate averages
        avg_vehicles_per_image = total_vehicles / total_images if total_images > 0 else 0
        avg_plates_per_image = readable_license_plates / total_images if total_images > 0 else 0
        
        # Create summary report
        summary_report = {
            'batch_summary': {
                'total_images_processed': total_images,
                'total_vehicles_detected': total_vehicles,
                'total_license_plates_detected': total_license_plates,
                'readable_license_plates': readable_license_plates,
                'average_vehicles_per_image': round(avg_vehicles_per_image, 2),
                'average_readable_plates_per_image': round(avg_plates_per_image, 2)
            },
            'vehicle_type_distribution': vehicle_type_counts,
            'all_license_plates': all_license_plates,
            'image_details': [
                {
                    'filename': result['metadata']['filename'],
                    'vehicles': result['summary']['total_vehicles'],
                    'readable_plates': result['summary']['readable_license_plates'],
                    'vehicle_types': list(result['vehicle_breakdown'].keys())
                }
                for result in results
            ]
        }
        
        return summary_report
    
    def save_csv_report(self, output_file: str, results: List[Dict[str, Any]] = None):
        """
        Save results as CSV file
        
        Args:
            output_file: Path to output CSV file
            results: List of analysis results (uses self.results if None)
        """
        if results is None:
            results = self.results
        
        if not results:
            print("⚠️  No results to save")
            return
        
        # Prepare CSV data
        csv_data = []
        for result in results:
            summary = result['summary']
            metadata = result['metadata']
            
            # Basic row data
            row = {
                'filename': metadata['filename'],
                'total_vehicles': summary['total_vehicles'],
                'total_license_plates': summary['total_license_plates_detected'],
                'readable_license_plates': summary['readable_license_plates'],
                'vehicle_types_count': summary['vehicle_types_detected']
            }
            
            # Add vehicle type counts
            for vehicle_type, count in result['vehicle_breakdown'].items():
                row[f'{vehicle_type}_count'] = count
            
            # Add license plate texts
            license_texts = [plate['text'] for plate in result['license_plates']]
            row['license_plate_texts'] = '; '.join(license_texts)
            
            csv_data.append(row)
        
        # Write CSV file
        if csv_data:
            fieldnames = set()
            for row in csv_data:
                fieldnames.update(row.keys())
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=sorted(fieldnames))
                writer.writeheader()
                writer.writerows(csv_data)
            
            print(f"💾 CSV report saved to: {output_file}")
    
    def print_summary_report(self, summary_report: Dict[str, Any] = None):
        """
        Print summary report to console
        
        Args:
            summary_report: Summary report dictionary
        """
        if summary_report is None:
            summary_report = self.generate_summary_report()
        
        print("\n" + "="*80)
        print("📊 BATCH PROCESSING SUMMARY REPORT")
        print("="*80)
        
        batch_summary = summary_report['batch_summary']
        print(f"📁 Images Processed: {batch_summary['total_images_processed']}")
        print(f"🚗 Total Vehicles Detected: {batch_summary['total_vehicles_detected']}")
        print(f"🔢 Total License Plates Detected: {batch_summary['total_license_plates_detected']}")
        print(f"📖 Readable License Plates: {batch_summary['readable_license_plates']}")
        print(f"📊 Average Vehicles per Image: {batch_summary['average_vehicles_per_image']}")
        print(f"📊 Average Readable Plates per Image: {batch_summary['average_readable_plates_per_image']}")
        
        print(f"\n🚙 VEHICLE TYPE DISTRIBUTION:")
        vehicle_distribution = summary_report['vehicle_type_distribution']
        if vehicle_distribution:
            for vehicle_type, count in sorted(vehicle_distribution.items()):
                percentage = (count / batch_summary['total_vehicles_detected']) * 100
                print(f"   {vehicle_type.capitalize()}: {count} ({percentage:.1f}%)")
        else:
            print("   No vehicles detected")
        
        print(f"\n🔢 ALL DETECTED LICENSE PLATES:")
        all_plates = summary_report['all_license_plates']
        if all_plates:
            for i, plate in enumerate(all_plates, 1):
                print(f"   {i}. {plate['text']} (from {plate['image']}, confidence: {plate['confidence']:.3f})")
        else:
            print("   No readable license plates found")
        
        print("="*80)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Batch analyze images for vehicles and license plates')
    parser.add_argument('input_dir', help='Directory containing input images')
    parser.add_argument('--output-dir', '-o', help='Directory to save annotated images')
    parser.add_argument('--device', default='cpu', choices=['cpu', 'cuda'], 
                       help='Device to use for inference')
    parser.add_argument('--json', help='Save detailed results as JSON file')
    parser.add_argument('--csv', help='Save summary results as CSV file')
    parser.add_argument('--summary-json', help='Save summary report as JSON file')
    
    args = parser.parse_args()
    
    # Check if input directory exists
    if not Path(args.input_dir).exists():
        print(f"❌ Error: Input directory '{args.input_dir}' not found!")
        return 1
    
    try:
        # Initialize batch analyzer
        batch_analyzer = BatchImageAnalyzer(device=args.device)
        
        # Process directory
        results = batch_analyzer.process_directory(args.input_dir, args.output_dir)
        
        if not results:
            print("❌ No images were successfully processed")
            return 1
        
        # Generate summary report
        summary_report = batch_analyzer.generate_summary_report(results)
        
        # Print summary
        batch_analyzer.print_summary_report(summary_report)
        
        # Save detailed results as JSON
        if args.json:
            with open(args.json, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"💾 Detailed results saved to JSON: {args.json}")
        
        # Save summary as JSON
        if args.summary_json:
            with open(args.summary_json, 'w') as f:
                json.dump(summary_report, f, indent=2)
            print(f"💾 Summary report saved to JSON: {args.summary_json}")
        
        # Save CSV report
        if args.csv:
            batch_analyzer.save_csv_report(args.csv, results)
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
