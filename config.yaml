# Traffic Surveillance System Configuration

# Video Input Configuration
video:
  # Input sources (can be file path, camera index, or RTSP stream)
  sources:
    - 0  # Use webcam (change to video file path or RTSP URL as needed)
    # - "sample_traffic.mp4"  # Example video file
    # - "rtsp://admin:password@*************:554/stream1"  # Example RTSP stream
  
  # Video processing settings
  fps: 30
  resolution:
    width: 1920
    height: 1080
  
  # Frame processing settings
  skip_frames: 2  # Process every nth frame for performance
  buffer_size: 10

# Model Configuration
models:
  # Vehicle Detection Model
  vehicle_detection:
    model_path: "models/yolov8n.pt"  # YOLOv8 nano for speed, use yolov8s.pt or yolov8m.pt for better accuracy
    confidence_threshold: 0.5
    iou_threshold: 0.45
    device: "cuda"  # "cuda" for GPU, "cpu" for CPU
    
  # License Plate Detection
  license_plate:
    model_path: "models/license_plate_detector.pt"
    confidence_threshold: 0.7
    ocr_engine: "easyocr"  # "easyocr" or "tesseract"
    
  # Traffic Sign Recognition
  traffic_signs:
    model_path: "models/traffic_sign_classifier.pt"
    confidence_threshold: 0.8

# Detection Classes
classes:
  vehicles: [2, 3, 5, 7]  # car, motorcycle, bus, truck (COCO class IDs)
  persons: [0]  # person
  traffic_lights: [9]  # traffic light

# Traffic Analytics
analytics:
  # Speed Estimation
  speed_estimation:
    enabled: true
    calibration_distance: 10  # meters
    calibration_pixels: 100   # pixels
    
  # Vehicle Counting
  counting:
    enabled: true
    counting_lines:
      - name: "Entry Line"
        coordinates: [[100, 400], [800, 400]]
      - name: "Exit Line"
        coordinates: [[100, 600], [800, 600]]
        
  # Traffic Flow Analysis
  flow_analysis:
    enabled: true
    congestion_threshold: 0.7  # Density threshold for congestion detection
    
  # Violation Detection
  violations:
    speed_limit: 60  # km/h
    red_light_violation: true
    wrong_way_detection: true
    parking_violation: true

# Database Configuration
database:
  type: "sqlite"  # "sqlite", "postgresql", "mysql"
  path: "data/traffic_surveillance.db"
  # For PostgreSQL/MySQL:
  # host: "localhost"
  # port: 5432
  # username: "user"
  # password: "password"
  # database: "traffic_db"

# Web Dashboard
dashboard:
  host: "0.0.0.0"
  port: 5000
  debug: false
  
# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/traffic_surveillance.log"
  max_file_size: "10MB"
  backup_count: 5

# Alert System
alerts:
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_password"
    recipients: ["<EMAIL>"]
    
  webhook:
    enabled: false
    url: "https://your-webhook-url.com/alerts"

# Performance Settings
performance:
  max_workers: 4  # Number of processing threads
  gpu_memory_fraction: 0.8  # GPU memory usage limit
  enable_tensorrt: false  # Enable TensorRT optimization (NVIDIA GPUs only)

# Storage Settings
storage:
  save_detections: true
  save_violations: true
  save_analytics: true
  retention_days: 30  # Days to keep data
  
  # Video recording
  record_violations: true
  video_format: "mp4"
  video_quality: "medium"  # "low", "medium", "high"
