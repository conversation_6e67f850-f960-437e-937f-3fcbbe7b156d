#!/usr/bin/env python3
"""
Traffic Surveillance System Demo
Demonstrates the system capabilities with sample data
"""

import sys
import time
import numpy as np
import cv2
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.models.vehicle_detector import VehicleDetector
from src.models.license_plate_detector import LicensePlateDetector
from src.analytics.traffic_analyzer import TrafficAnalyzer
from src.utils.config_manager import ConfigManager
from src.utils.database_manager import DatabaseManager


def create_demo_frame():
    """Create a demo frame with simulated traffic"""
    # Create a 640x480 frame
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Add road background
    cv2.rectangle(frame, (0, 200), (640, 400), (50, 50, 50), -1)  # Road
    cv2.line(frame, (0, 300), (640, 300), (255, 255, 255), 2)     # Lane divider
    
    # Add some "vehicles" (rectangles)
    vehicles = [
        (100, 250, 180, 320, "Car 1"),
        (300, 220, 380, 280, "Car 2"),
        (500, 260, 580, 330, "Truck 1")
    ]
    
    colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255)]
    
    for i, (x1, y1, x2, y2, label) in enumerate(vehicles):
        color = colors[i % len(colors)]
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    
    # Add title
    cv2.putText(frame, "Traffic Surveillance Demo", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return frame


def demo_vehicle_detection():
    """Demonstrate vehicle detection capabilities"""
    print("🚗 Vehicle Detection Demo")
    print("=" * 50)
    
    try:
        # Initialize detector (will download YOLOv8 model on first run)
        print("Initializing vehicle detector...")
        detector = VehicleDetector(device="cpu")  # Use CPU for demo
        
        # Create demo frame
        frame = create_demo_frame()
        
        # Since we're using a synthetic frame, let's simulate detections
        simulated_detections = [
            {
                'bbox': [100, 250, 180, 320],
                'confidence': 0.85,
                'class_id': 2,
                'class_name': 'car',
                'center': (140, 285),
                'area': 5600
            },
            {
                'bbox': [300, 220, 380, 280],
                'confidence': 0.92,
                'class_id': 2,
                'class_name': 'car',
                'center': (340, 250),
                'area': 4800
            },
            {
                'bbox': [500, 260, 580, 330],
                'confidence': 0.78,
                'class_id': 7,
                'class_name': 'truck',
                'center': (540, 295),
                'area': 5600
            }
        ]
        
        # Draw detections
        frame_with_detections = detector.draw_detections(frame, simulated_detections)
        
        # Display results
        print(f"✅ Detected {len(simulated_detections)} vehicles:")
        for i, detection in enumerate(simulated_detections, 1):
            print(f"   {i}. {detection['class_name'].title()} - Confidence: {detection['confidence']:.2f}")
        
        # Save demo image
        cv2.imwrite("demo_detection.jpg", frame_with_detections)
        print("📸 Demo image saved as 'demo_detection.jpg'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in vehicle detection demo: {str(e)}")
        return False


def demo_traffic_analytics():
    """Demonstrate traffic analytics capabilities"""
    print("\n📊 Traffic Analytics Demo")
    print("=" * 50)
    
    try:
        # Initialize analytics
        config = {
            'speed_estimation': {
                'enabled': True,
                'calibration_distance': 10,
                'calibration_pixels': 100
            },
            'counting': {
                'enabled': True,
                'counting_lines': [
                    {
                        'name': 'Entry Line',
                        'coordinates': [[50, 300], [590, 300]]
                    }
                ]
            },
            'violations': {
                'speed_limit': 60,
                'red_light_violation': True
            }
        }
        
        analyzer = TrafficAnalyzer(config)
        
        # Simulate detection data
        detections = {
            'vehicles': [
                {
                    'bbox': [100, 250, 180, 320],
                    'confidence': 0.85,
                    'class_name': 'car',
                    'center': (140, 285)
                },
                {
                    'bbox': [300, 220, 380, 280],
                    'confidence': 0.92,
                    'class_name': 'car',
                    'center': (340, 250)
                }
            ],
            'timestamp': time.time()
        }
        
        # Analyze frame
        frame = create_demo_frame()
        analytics_result = analyzer.analyze_frame(frame, detections, time.time())
        
        # Display results
        print(f"✅ Analytics Results:")
        print(f"   Vehicle Count: {analytics_result['vehicle_count']}")
        print(f"   Tracked Vehicles: {analytics_result['tracked_vehicles']}")
        print(f"   Average Speed: {analytics_result['average_speed']:.1f} km/h")
        print(f"   Traffic Flow: {analytics_result['traffic_flow']['status']}")
        print(f"   Violations: {analytics_result['violations_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in traffic analytics demo: {str(e)}")
        return False


def demo_database():
    """Demonstrate database functionality"""
    print("\n💾 Database Demo")
    print("=" * 50)
    
    try:
        # Initialize database
        config = {
            'type': 'sqlite',
            'path': 'demo_traffic.db'
        }
        
        db_manager = DatabaseManager(config)
        
        # Save sample detection
        sample_detection = {
            'class_name': 'car',
            'confidence': 0.85,
            'bbox': [100, 100, 200, 200],
            'center': (150, 150),
            'area': 10000
        }
        
        detection_id = db_manager.save_vehicle_detection(sample_detection, time.time())
        
        # Save sample violation
        sample_violation = {
            'type': 'speed_violation',
            'severity': 'medium',
            'speed': 75.5,
            'speed_limit': 60,
            'position': (150, 150)
        }
        
        violation_id = db_manager.save_violation(sample_violation, time.time())
        
        # Get statistics
        stats = db_manager.get_database_stats()
        
        print(f"✅ Database Operations:")
        print(f"   Detection saved with ID: {detection_id}")
        print(f"   Violation saved with ID: {violation_id}")
        print(f"   Database statistics: {stats}")
        
        db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Error in database demo: {str(e)}")
        return False


def demo_configuration():
    """Demonstrate configuration management"""
    print("\n⚙️ Configuration Demo")
    print("=" * 50)
    
    try:
        # Load configuration
        config_manager = ConfigManager('config.yaml')
        
        # Get configuration sections
        video_config = config_manager.get_section('video')
        models_config = config_manager.get_section('models')
        
        print(f"✅ Configuration loaded successfully:")
        print(f"   Video sources: {len(video_config.get('sources', []))}")
        print(f"   Video FPS: {video_config.get('fps', 'Not set')}")
        print(f"   Models configured: {list(models_config.keys())}")
        
        # Check feature status
        speed_enabled = config_manager.is_feature_enabled('analytics.speed_estimation.enabled')
        counting_enabled = config_manager.is_feature_enabled('analytics.counting.enabled')
        
        print(f"   Speed estimation: {'✅ Enabled' if speed_enabled else '❌ Disabled'}")
        print(f"   Vehicle counting: {'✅ Enabled' if counting_enabled else '❌ Disabled'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in configuration demo: {str(e)}")
        return False


def main():
    """Run all demos"""
    print("🚦 Traffic Surveillance System - Demo Mode")
    print("=" * 60)
    print("This demo showcases the key features of the traffic surveillance system.")
    print()
    
    demos = [
        ("Configuration Management", demo_configuration),
        ("Vehicle Detection", demo_vehicle_detection),
        ("Traffic Analytics", demo_traffic_analytics),
        ("Database Operations", demo_database)
    ]
    
    results = []
    
    for name, demo_func in demos:
        try:
            success = demo_func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ {name} failed: {str(e)}")
            results.append((name, False))
    
    # Summary
    print("\n📋 Demo Summary")
    print("=" * 50)
    
    successful = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {name}: {status}")
    
    print(f"\nOverall: {successful}/{total} demos completed successfully")
    
    if successful == total:
        print("\n🎉 All demos completed successfully!")
        print("The traffic surveillance system is ready to use.")
        print("\nTo run the full system:")
        print("   python main.py                    # Complete system")
        print("   python main.py --mode dashboard   # Web dashboard only")
        print("   python main.py --video 0          # Use webcam")
        print("\nWeb dashboard: http://localhost:5000")
    else:
        print(f"\n⚠️  {total - successful} demo(s) failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
