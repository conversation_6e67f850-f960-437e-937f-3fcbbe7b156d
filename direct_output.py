#!/usr/bin/env python3
"""
Direct Output Demo - Shows results immediately in terminal
"""

import cv2
import numpy as np
import json

def create_traffic_scene():
    """Create a sample traffic image"""
    # Create image
    img = np.ones((400, 600, 3), dtype=np.uint8) * 60
    
    # Draw road
    cv2.rectangle(img, (0, 200), (600, 400), (70, 70, 70), -1)
    
    # Create vehicles (simulating detection)
    vehicles = [
        {"type": "car", "pos": (50, 220), "size": (60, 30), "plate": "ABC123"},
        {"type": "car", "pos": (150, 240), "size": (65, 32), "plate": "XYZ789"},
        {"type": "truck", "pos": (280, 210), "size": (90, 50), "plate": "TRK456"},
        {"type": "car", "pos": (420, 230), "size": (58, 28), "plate": ""},
        {"type": "motorcycle", "pos": (520, 250), "size": (35, 20), "plate": ""},
        {"type": "car", "pos": (80, 320), "size": (62, 30), "plate": "DEF789"},
        {"type": "car", "pos": (200, 340), "size": (64, 31), "plate": ""},
    ]
    
    # Draw vehicles
    colors = {"car": (0, 0, 200), "truck": (0, 150, 0), "motorcycle": (200, 200, 0)}
    for vehicle in vehicles:
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        color = colors[vehicle["type"]]
        cv2.rectangle(img, (x, y), (x+w, y+h), color, -1)
        cv2.rectangle(img, (x, y), (x+w, y+h), (255, 255, 255), 1)
    
    return img, vehicles

def analyze_and_show_results():
    """Analyze image and show direct output"""
    
    print("🚗" * 30)
    print("    VEHICLE DETECTION OUTPUT")
    print("🚗" * 30)
    
    # Create sample scene
    image, vehicles = create_traffic_scene()
    
    # Count vehicles by type
    vehicle_counts = {}
    for vehicle in vehicles:
        vtype = vehicle["type"]
        vehicle_counts[vtype] = vehicle_counts.get(vtype, 0) + 1
    
    # Count license plates
    plates_with_text = [v for v in vehicles if v["plate"]]
    
    print(f"\n📊 DETECTION SUMMARY:")
    print(f"   Total Vehicles: {len(vehicles)}")
    print(f"   License Plates Found: {len(plates_with_text)}")
    print(f"   Vehicle Types: {len(vehicle_counts)}")
    
    print(f"\n🚙 VEHICLE BREAKDOWN:")
    for vtype, count in vehicle_counts.items():
        percentage = (count / len(vehicles)) * 100
        print(f"   {vtype.capitalize()}: {count} ({percentage:.1f}%)")
    
    print(f"\n🔢 LICENSE PLATES DETECTED:")
    if plates_with_text:
        for i, vehicle in enumerate(plates_with_text, 1):
            confidence = 0.75 + (i * 0.05)  # Simulate confidence
            print(f"   {i}. {vehicle['plate']} (Confidence: {confidence:.2f})")
    else:
        print("   No readable license plates found")
    
    print(f"\n📋 DETAILED VEHICLE LIST:")
    for i, vehicle in enumerate(vehicles, 1):
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        confidence = 0.80 + (i * 0.02)  # Simulate confidence
        print(f"   {i}. {vehicle['type'].upper()}")
        print(f"      Position: ({x}, {y})")
        print(f"      Size: {w}x{h} pixels")
        print(f"      Confidence: {confidence:.2f}")
        if vehicle["plate"]:
            print(f"      License Plate: {vehicle['plate']}")
        print()
    
    # Show ASCII representation of the scene
    print("🖼️  VISUAL REPRESENTATION:")
    print("=" * 60)
    print("                    TRAFFIC SCENE")
    print("=" * 60)
    print("🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢🏢")
    print("                                                    ")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    # Show vehicles in ASCII
    road_line1 = "🚗     🚗       🚚         🚗      🏍️           "
    road_line2 = "      🚗       🚗                              "
    
    print(road_line1)
    print(road_line2)
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    # Show detection boxes representation
    print("\n📦 DETECTION BOXES:")
    print("   🟢 = Car detected")
    print("   🟢 = Truck detected") 
    print("   🟢 = Motorcycle detected")
    print("   🔢 = License plate readable")
    
    detection_map = ""
    for vehicle in vehicles:
        if vehicle["type"] == "car":
            detection_map += "🟢🚗 "
        elif vehicle["type"] == "truck":
            detection_map += "🟢🚚 "
        elif vehicle["type"] == "motorcycle":
            detection_map += "🟢🏍️ "
        
        if vehicle["plate"]:
            detection_map += "🔢 "
    
    print(f"   Detections: {detection_map}")
    
    print("\n" + "🚗" * 30)
    print("         ANALYSIS COMPLETE!")
    print("🚗" * 30)
    
    # Create summary data
    results = {
        "total_vehicles": len(vehicles),
        "vehicle_types": vehicle_counts,
        "license_plates": len(plates_with_text),
        "readable_plates": [v["plate"] for v in plates_with_text],
        "confidence_range": "0.80 - 0.94"
    }
    
    print(f"\n📄 JSON SUMMARY:")
    print(json.dumps(results, indent=2))
    
    return results

if __name__ == "__main__":
    analyze_and_show_results()
