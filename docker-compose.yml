version: '3.8'

services:
  traffic-surveillance:
    build: .
    container_name: traffic-surveillance-system
    ports:
      - "5000:5000"  # Web dashboard
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      - ./config.yaml:/app/config.yaml
      - /dev/video0:/dev/video0  # Camera access (Linux only)
    devices:
      - /dev/video0:/dev/video0  # Camera access (Linux only)
    environment:
      - PYTHONPATH=/app/src
      - DISPLAY=${DISPLAY}  # For GUI display (Linux only)
    networks:
      - surveillance-network
    restart: unless-stopped
    depends_on:
      - database
    
  database:
    image: postgres:13
    container_name: traffic-surveillance-db
    environment:
      POSTGRES_DB: traffic_surveillance
      POSTGRES_USER: traffic_user
      POSTGRES_PASSWORD: traffic_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - surveillance-network
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    container_name: traffic-surveillance-redis
    ports:
      - "6379:6379"
    networks:
      - surveillance-network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: traffic-surveillance-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - traffic-surveillance
    networks:
      - surveillance-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  surveillance-network:
    driver: bridge
