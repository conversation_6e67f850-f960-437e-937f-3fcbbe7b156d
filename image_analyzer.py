#!/usr/bin/env python3
"""
Image-based Vehicle Detection and Analysis
Input: Image file
Output: Vehicle count, types, and license plates detected
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
import json
from typing import Dict, List, Any

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.models.vehicle_detector import VehicleDetector
from src.models.license_plate_detector import LicensePlateDetector


class ImageVehicleAnalyzer:
    """
    Analyzes a single image for vehicles, license plates, and vehicle types
    """
    
    def __init__(self, device: str = "cpu"):
        """
        Initialize the analyzer
        
        Args:
            device: Device to use for inference ('cpu' or 'cuda')
        """
        self.device = device
        
        # Initialize detectors
        print("🚗 Initializing vehicle detector...")
        self.vehicle_detector = VehicleDetector(device=device)
        
        print("🔍 Initializing license plate detector...")
        try:
            self.license_plate_detector = LicensePlateDetector(device=device)
        except Exception as e:
            print(f"⚠️  License plate detector not available: {str(e)}")
            self.license_plate_detector = None
    
    def analyze_image(self, image_path: str, output_path: str = None, 
                     show_image: bool = True) -> Dict[str, Any]:
        """
        Analyze an image for vehicles and license plates
        
        Args:
            image_path: Path to input image
            output_path: Path to save annotated image (optional)
            show_image: Whether to display the result image
            
        Returns:
            Dictionary containing analysis results
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        print(f"📸 Analyzing image: {image_path}")
        print(f"📏 Image dimensions: {image.shape[1]}x{image.shape[0]}")
        
        # Detect vehicles
        print("🔍 Detecting vehicles...")
        vehicle_detections = self.vehicle_detector.detect_vehicles(image)
        
        # Detect license plates
        license_plate_detections = []
        if self.license_plate_detector:
            print("🔍 Detecting license plates...")
            lp_detections = self.license_plate_detector.detect_license_plates(image)
            license_plate_detections = self.license_plate_detector.recognize_license_plates(
                image, lp_detections
            )
        
        # Analyze results
        results = self._analyze_detections(vehicle_detections, license_plate_detections)
        
        # Draw annotations on image
        annotated_image = self._draw_annotations(
            image.copy(), vehicle_detections, license_plate_detections
        )
        
        # Save annotated image
        if output_path:
            cv2.imwrite(output_path, annotated_image)
            print(f"💾 Annotated image saved to: {output_path}")
        
        # Display image
        if show_image:
            self._display_image(annotated_image, results)
        
        return results
    
    def _analyze_detections(self, vehicle_detections: List[Dict], 
                          license_plate_detections: List[Dict]) -> Dict[str, Any]:
        """
        Analyze detection results and compile statistics
        
        Args:
            vehicle_detections: List of vehicle detections
            license_plate_detections: List of license plate detections
            
        Returns:
            Analysis results dictionary
        """
        # Count vehicles by type
        vehicle_counts = {}
        total_vehicles = len(vehicle_detections)
        
        for detection in vehicle_detections:
            vehicle_type = detection['class_name']
            vehicle_counts[vehicle_type] = vehicle_counts.get(vehicle_type, 0) + 1
        
        # Process license plates
        license_plates = []
        readable_plates = 0
        
        for plate_detection in license_plate_detections:
            plate_text = plate_detection.get('text', '').strip()
            if plate_text:
                license_plates.append({
                    'text': plate_text,
                    'confidence': plate_detection['confidence'],
                    'bbox': plate_detection['bbox']
                })
                readable_plates += 1
        
        # Compile results
        results = {
            'summary': {
                'total_vehicles': total_vehicles,
                'total_license_plates_detected': len(license_plate_detections),
                'readable_license_plates': readable_plates,
                'vehicle_types_detected': len(vehicle_counts)
            },
            'vehicle_breakdown': vehicle_counts,
            'license_plates': license_plates,
            'detailed_vehicles': [
                {
                    'type': det['class_name'],
                    'confidence': round(det['confidence'], 3),
                    'bbox': det['bbox'],
                    'center': det['center']
                }
                for det in vehicle_detections
            ]
        }
        
        return results
    
    def _draw_annotations(self, image: np.ndarray, vehicle_detections: List[Dict], 
                         license_plate_detections: List[Dict]) -> np.ndarray:
        """
        Draw detection annotations on the image
        
        Args:
            image: Input image
            vehicle_detections: Vehicle detections
            license_plate_detections: License plate detections
            
        Returns:
            Annotated image
        """
        # Draw vehicle detections
        image = self.vehicle_detector.draw_detections(image, vehicle_detections)
        
        # Draw license plate detections
        if self.license_plate_detector and license_plate_detections:
            image = self.license_plate_detector.draw_license_plates(image, license_plate_detections)
        
        # Add summary text
        summary_text = [
            f"Vehicles: {len(vehicle_detections)}",
            f"License Plates: {len(license_plate_detections)}"
        ]
        
        # Draw summary box
        y_offset = 30
        for i, text in enumerate(summary_text):
            cv2.rectangle(image, (10, 10 + i * 30), (300, 40 + i * 30), (0, 0, 0), -1)
            cv2.putText(image, text, (20, 35 + i * 30), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.7, (255, 255, 255), 2)
        
        return image
    
    def _display_image(self, image: np.ndarray, results: Dict[str, Any]):
        """
        Display the annotated image and results
        
        Args:
            image: Annotated image
            results: Analysis results
        """
        # Resize image if too large
        height, width = image.shape[:2]
        if width > 1200 or height > 800:
            scale = min(1200/width, 800/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))
        
        # Display image
        cv2.imshow('Vehicle Detection Results', image)
        
        # Print results
        self._print_results(results)
        
        print("\n📋 Press any key to close the image window...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    def _print_results(self, results: Dict[str, Any]):
        """
        Print analysis results to console
        
        Args:
            results: Analysis results dictionary
        """
        print("\n" + "="*60)
        print("🚗 VEHICLE DETECTION RESULTS")
        print("="*60)
        
        summary = results['summary']
        print(f"📊 SUMMARY:")
        print(f"   Total Vehicles Detected: {summary['total_vehicles']}")
        print(f"   License Plates Detected: {summary['total_license_plates_detected']}")
        print(f"   Readable License Plates: {summary['readable_license_plates']}")
        print(f"   Vehicle Types Found: {summary['vehicle_types_detected']}")
        
        print(f"\n🚙 VEHICLE BREAKDOWN:")
        vehicle_breakdown = results['vehicle_breakdown']
        if vehicle_breakdown:
            for vehicle_type, count in vehicle_breakdown.items():
                print(f"   {vehicle_type.capitalize()}: {count}")
        else:
            print("   No vehicles detected")
        
        print(f"\n🔢 LICENSE PLATES:")
        license_plates = results['license_plates']
        if license_plates:
            for i, plate in enumerate(license_plates, 1):
                print(f"   {i}. {plate['text']} (Confidence: {plate['confidence']:.3f})")
        else:
            print("   No readable license plates found")
        
        print(f"\n📋 DETAILED VEHICLE LIST:")
        detailed_vehicles = results['detailed_vehicles']
        if detailed_vehicles:
            for i, vehicle in enumerate(detailed_vehicles, 1):
                print(f"   {i}. {vehicle['type'].capitalize()} - "
                      f"Confidence: {vehicle['confidence']:.3f} - "
                      f"Position: {vehicle['center']}")
        else:
            print("   No vehicles detected")
        
        print("="*60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Analyze image for vehicles and license plates')
    parser.add_argument('image', help='Path to input image')
    parser.add_argument('--output', '-o', help='Path to save annotated image')
    parser.add_argument('--device', default='cpu', choices=['cpu', 'cuda'], 
                       help='Device to use for inference')
    parser.add_argument('--no-display', action='store_true', 
                       help='Do not display the result image')
    parser.add_argument('--json', help='Save results as JSON file')
    
    args = parser.parse_args()
    
    # Check if input image exists
    if not Path(args.image).exists():
        print(f"❌ Error: Image file '{args.image}' not found!")
        return 1
    
    try:
        # Initialize analyzer
        analyzer = ImageVehicleAnalyzer(device=args.device)
        
        # Set output path if not specified
        output_path = args.output
        if not output_path:
            input_path = Path(args.image)
            output_path = str(input_path.parent / f"{input_path.stem}_analyzed{input_path.suffix}")
        
        # Analyze image
        results = analyzer.analyze_image(
            args.image, 
            output_path=output_path,
            show_image=not args.no_display
        )
        
        # Save JSON results if requested
        if args.json:
            with open(args.json, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"💾 Results saved to JSON: {args.json}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
