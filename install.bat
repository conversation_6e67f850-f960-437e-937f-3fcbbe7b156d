@echo off
REM Installation script for Traffic Surveillance System (Windows)

echo ========================================
echo Traffic Surveillance System Installer
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking version...
python -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"
if errorlevel 1 (
    echo ERROR: Python 3.8 or higher is required
    pause
    exit /b 1
)

echo Python version is compatible.
echo.

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv
if errorlevel 1 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install requirements
    pause
    exit /b 1
)

REM Create necessary directories
echo Creating directories...
if not exist "models" mkdir models
if not exist "data" mkdir data
if not exist "logs" mkdir logs

REM Download YOLOv8 model (will be downloaded automatically on first run)
echo.
echo Installation completed successfully!
echo.
echo To run the system:
echo 1. Activate the virtual environment: venv\Scripts\activate.bat
echo 2. Run the system: python main.py
echo.
echo For help: python main.py --help
echo.
pause
