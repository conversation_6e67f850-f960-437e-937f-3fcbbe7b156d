#!/bin/bash
# Installation script for Traffic Surveillance System (Linux/macOS)

set -e

echo "========================================"
echo "Traffic Surveillance System Installer"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 is not installed"
    echo "Please install Python 3.8 or higher"
    exit 1
fi

echo "Python found. Checking version..."
python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" || {
    echo "ERROR: Python 3.8 or higher is required"
    exit 1
}

echo "Python version is compatible."
echo

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Create necessary directories
echo "Creating directories..."
mkdir -p models data logs

# Set permissions
chmod +x main.py

echo
echo "Installation completed successfully!"
echo
echo "To run the system:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Run the system: python main.py"
echo
echo "For help: python main.py --help"
echo
