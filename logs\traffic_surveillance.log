2025-08-23 19:14:18,672 - traffic_surveillance - INFO - Starting Traffic Surveillance System
2025-08-23 19:14:18,674 - traffic_surveillance - INFO - Configuration loaded from: config.yaml
2025-08-23 19:14:18,676 - traffic_surveillance - INFO - Run mode: dashboard
2025-08-23 19:14:18,677 - traffic_surveillance - INFO - Starting web dashboard...
2025-08-23 19:14:18,759 - src.utils.database_manager - INFO - Connected to SQLite database: data/traffic_surveillance.db
2025-08-23 19:14:18,837 - src.utils.database_manager - INFO - Database tables created successfully
2025-08-23 19:14:18,877 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-08-23 19:14:18,878 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-23 19:14:18,878 - werkzeug - INFO -  * Restarting with stat
2025-08-23 19:14:27,275 - traffic_surveillance - INFO - Starting Traffic Surveillance System
2025-08-23 19:14:27,276 - traffic_surveillance - INFO - Configuration loaded from: config.yaml
2025-08-23 19:14:27,277 - traffic_surveillance - INFO - Run mode: dashboard
2025-08-23 19:14:27,277 - traffic_surveillance - INFO - Starting web dashboard...
2025-08-23 19:14:27,353 - src.utils.database_manager - INFO - Connected to SQLite database: data/traffic_surveillance.db
2025-08-23 19:14:27,358 - src.utils.database_manager - INFO - Database tables created successfully
2025-08-23 19:14:27,392 - werkzeug - WARNING -  * Debugger is active!
2025-08-23 19:14:27,400 - werkzeug - INFO -  * Debugger PIN: 504-450-038
2025-08-23 19:14:56,772 - traffic_surveillance - INFO - Starting Traffic Surveillance System
2025-08-23 19:14:56,773 - traffic_surveillance - INFO - Configuration loaded from: config.yaml
2025-08-23 19:14:56,773 - traffic_surveillance - INFO - Run mode: dashboard
2025-08-23 19:14:56,773 - traffic_surveillance - INFO - Starting web dashboard...
2025-08-23 19:14:56,835 - src.utils.database_manager - INFO - Connected to SQLite database: data/traffic_surveillance.db
2025-08-23 19:14:56,837 - src.utils.database_manager - INFO - Database tables created successfully
2025-08-23 19:14:56,879 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-08-23 19:14:56,879 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-23 19:14:56,879 - werkzeug - INFO -  * Restarting with stat
2025-08-23 19:15:05,099 - traffic_surveillance - INFO - Starting Traffic Surveillance System
2025-08-23 19:15:05,099 - traffic_surveillance - INFO - Configuration loaded from: config.yaml
2025-08-23 19:15:05,100 - traffic_surveillance - INFO - Run mode: dashboard
2025-08-23 19:15:05,100 - traffic_surveillance - INFO - Starting web dashboard...
2025-08-23 19:15:05,160 - src.utils.database_manager - INFO - Connected to SQLite database: data/traffic_surveillance.db
2025-08-23 19:15:05,163 - src.utils.database_manager - INFO - Database tables created successfully
2025-08-23 19:15:05,185 - werkzeug - WARNING -  * Debugger is active!
2025-08-23 19:15:05,191 - werkzeug - INFO -  * Debugger PIN: 504-450-038
2025-08-23 19:15:21,492 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:21] "GET / HTTP/1.1" 200 -
2025-08-23 19:15:21,986 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:21] "GET /video-feed HTTP/1.1" 200 -
2025-08-23 19:15:22,534 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:22] "GET /static/css/dashboard.css HTTP/1.1" 200 -
2025-08-23 19:15:22,692 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:22] "GET /static/js/dashboard.js HTTP/1.1" 200 -
2025-08-23 19:15:22,716 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:22] "GET /socket.io/?EIO=4&transport=polling&t=PZNBJmI HTTP/1.1" 200 -
2025-08-23 19:15:23,044 - src.web.dashboard - INFO - Client connected: B0gPpY2NEeQ5o_BEAAAB
2025-08-23 19:15:23,058 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:23] "GET /socket.io/?EIO=4&transport=polling&t=PZNBJrX&sid=v9t82PcRV5W_ym70AAAA HTTP/1.1" 200 -
2025-08-23 19:15:23,061 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-23 19:15:23,067 - werkzeug - INFO - 127.0.0.1 - - [23/Aug/2025 19:15:23] "POST /socket.io/?EIO=4&transport=polling&t=PZNBJrQ&sid=v9t82PcRV5W_ym70AAAA HTTP/1.1" 200 -
