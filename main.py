#!/usr/bin/env python3
"""
Real-time Traffic Surveillance and Detection System
Main application entry point
"""

import argparse
import sys
import os
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.surveillance_system import TrafficSurveillanceSystem
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger
from src.web.dashboard import create_app


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Real-time Traffic Surveillance and Detection System"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file (default: config.yaml)"
    )
    
    parser.add_argument(
        "--mode",
        type=str,
        choices=["surveillance", "dashboard", "both"],
        default="both",
        help="Run mode: surveillance only, dashboard only, or both (default: both)"
    )
    
    parser.add_argument(
        "--video",
        type=str,
        help="Override video source from config (file path, camera index, or RTSP URL)"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        help="Output directory for results (optional)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--no-display",
        action="store_true",
        help="Run without video display (headless mode)"
    )
    
    return parser.parse_args()


def main():
    """Main application entry point."""
    args = parse_arguments()
    
    try:
        # Load configuration
        config_manager = ConfigManager(args.config)
        config = config_manager.get_config()
        
        # Override config with command line arguments
        if args.video:
            config['video']['sources'] = [args.video]
        
        if args.debug:
            config['logging']['level'] = 'DEBUG'
            config['dashboard']['debug'] = True
        
        # Setup logging
        logger = setup_logger(config['logging'])
        logger.info("Starting Traffic Surveillance System")
        logger.info(f"Configuration loaded from: {args.config}")
        logger.info(f"Run mode: {args.mode}")
        
        # Create output directory if specified
        if args.output:
            os.makedirs(args.output, exist_ok=True)
            logger.info(f"Output directory: {args.output}")
        
        # Initialize and run the system based on mode
        if args.mode in ["surveillance", "both"]:
            # Initialize surveillance system
            surveillance_system = TrafficSurveillanceSystem(
                config=config,
                output_dir=args.output,
                headless=args.no_display
            )
            
            if args.mode == "surveillance":
                # Run surveillance only
                logger.info("Starting surveillance system...")
                surveillance_system.run()
            
            elif args.mode == "both":
                # Run both surveillance and dashboard
                logger.info("Starting surveillance system and web dashboard...")
                
                # Start surveillance in a separate thread
                import threading
                surveillance_thread = threading.Thread(
                    target=surveillance_system.run,
                    daemon=True
                )
                surveillance_thread.start()
                
                # Start web dashboard
                app = create_app(config, surveillance_system)
                app.run(
                    host=config['dashboard']['host'],
                    port=config['dashboard']['port'],
                    debug=config['dashboard']['debug']
                )
        
        elif args.mode == "dashboard":
            # Run dashboard only (assumes surveillance data exists)
            logger.info("Starting web dashboard...")
            app = create_app(config)
            app.run(
                host=config['dashboard']['host'],
                port=config['dashboard']['port'],
                debug=config['dashboard']['debug']
            )
    
    except KeyboardInterrupt:
        logger.info("Received interrupt signal. Shutting down...")
        sys.exit(0)
    
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
        if args.debug:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
