#!/usr/bin/env python3
"""
Quick Demo Script for Vehicle Detection
This script provides a simple way to test vehicle detection on images
without requiring the full surveillance system setup.
"""

import cv2
import numpy as np
from pathlib import Path
import sys
import json

def simple_vehicle_detection_demo(image_path: str):
    """
    Simple demo using basic OpenCV and pre-trained models
    This is a fallback demo if the full system isn't available
    """
    print(f"🔍 Loading image: {image_path}")
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Could not load image: {image_path}")
        return None
    
    print(f"📏 Image size: {image.shape[1]}x{image.shape[0]} pixels")
    
    # For this demo, we'll use a simple approach
    # In a real implementation, you would use YOLO or similar models
    
    # Convert to different color spaces for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Simple vehicle-like object detection using contours
    # This is a very basic approach for demonstration
    
    # Apply some preprocessing
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)
    
    # Find contours
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours that might be vehicles (basic heuristics)
    potential_vehicles = []
    min_area = 1000  # Minimum area for a vehicle
    max_area = image.shape[0] * image.shape[1] * 0.3  # Maximum 30% of image
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Basic aspect ratio check (vehicles are typically wider than tall)
            aspect_ratio = w / h
            if 0.5 < aspect_ratio < 4.0:  # Reasonable aspect ratio for vehicles
                potential_vehicles.append({
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect_ratio': aspect_ratio,
                    'type': 'vehicle',  # Generic type for this demo
                    'confidence': min(0.9, area / 10000)  # Fake confidence based on size
                })
    
    # Sort by area (larger objects first)
    potential_vehicles.sort(key=lambda x: x['area'], reverse=True)
    
    # Take top detections (limit to reasonable number)
    max_detections = 20
    vehicles = potential_vehicles[:max_detections]
    
    # Create results
    results = {
        'image_info': {
            'filename': Path(image_path).name,
            'dimensions': f"{image.shape[1]}x{image.shape[0]}",
            'total_pixels': image.shape[0] * image.shape[1]
        },
        'detection_summary': {
            'total_vehicles': len(vehicles),
            'vehicle_types_found': 1 if vehicles else 0,
            'license_plates_detected': 0,  # Not implemented in this simple demo
            'readable_license_plates': 0
        },
        'vehicle_breakdown': {
            'vehicle': len(vehicles)
        } if vehicles else {},
        'license_plates': [],  # Not implemented in this simple demo
        'detailed_detections': {
            'vehicles': [
                {
                    'type': 'vehicle',
                    'confidence': round(v['confidence'], 3),
                    'bbox': v['bbox'],
                    'area': v['area']
                }
                for v in vehicles
            ]
        }
    }
    
    # Draw detections on image
    annotated_image = image.copy()
    
    for i, vehicle in enumerate(vehicles):
        x, y, w, h = vehicle['bbox']
        
        # Draw bounding box
        cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # Draw label
        label = f"Vehicle {i+1} ({vehicle['confidence']:.2f})"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
        cv2.rectangle(annotated_image, (x, y - label_size[1] - 10), 
                     (x + label_size[0], y), (0, 255, 0), -1)
        cv2.putText(annotated_image, label, (x, y - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2)
    
    # Add summary text
    summary_text = f"Detected: {len(vehicles)} vehicles"
    cv2.rectangle(annotated_image, (10, 10), (400, 50), (0, 0, 0), -1)
    cv2.putText(annotated_image, summary_text, (20, 35), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return results, annotated_image


def print_demo_results(results):
    """Print results in a nice format"""
    print("\n" + "="*50)
    print("🚗 SIMPLE VEHICLE DETECTION DEMO")
    print("="*50)
    
    image_info = results['image_info']
    print(f"📸 Image: {image_info['filename']}")
    print(f"📏 Size: {image_info['dimensions']}")
    
    summary = results['detection_summary']
    print(f"\n📊 RESULTS:")
    print(f"   Vehicles detected: {summary['total_vehicles']}")
    
    if summary['total_vehicles'] > 0:
        print(f"\n📋 DETECTED VEHICLES:")
        for i, vehicle in enumerate(results['detailed_detections']['vehicles'], 1):
            x, y, w, h = vehicle['bbox']
            print(f"   {i}. Vehicle at ({x}, {y}) - "
                  f"Size: {w}x{h} - "
                  f"Confidence: {vehicle['confidence']:.3f}")
    
    print("\n⚠️  NOTE: This is a basic demo using simple computer vision.")
    print("   For accurate vehicle detection, use the full system with YOLO models.")
    print("="*50)


def main():
    """Main demo function"""
    if len(sys.argv) != 2:
        print("Usage: python quick_demo.py <image_path>")
        print("\nExample:")
        print("  python quick_demo.py traffic_image.jpg")
        return 1
    
    image_path = sys.argv[1]
    
    # Check if image exists
    if not Path(image_path).exists():
        print(f"❌ Image file not found: {image_path}")
        return 1
    
    try:
        print("🚀 Starting simple vehicle detection demo...")
        print("   This demo uses basic OpenCV for demonstration purposes.")
        print("   For production use, please use the full system with YOLO models.\n")
        
        # Run detection
        results, annotated_image = simple_vehicle_detection_demo(image_path)
        
        if results is None:
            return 1
        
        # Print results
        print_demo_results(results)
        
        # Save annotated image
        output_path = f"demo_result_{Path(image_path).stem}.jpg"
        cv2.imwrite(output_path, annotated_image)
        print(f"\n💾 Annotated image saved as: {output_path}")
        
        # Save results as JSON
        json_path = f"demo_results_{Path(image_path).stem}.json"
        with open(json_path, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"💾 Results saved as JSON: {json_path}")
        
        # Display image
        print(f"\n📺 Displaying result image...")
        print("   Press any key to close the window.")
        
        # Resize if too large
        height, width = annotated_image.shape[:2]
        if width > 1000 or height > 700:
            scale = min(1000/width, 700/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            display_image = cv2.resize(annotated_image, (new_width, new_height))
        else:
            display_image = annotated_image
        
        cv2.imshow('Vehicle Detection Demo', display_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
        
        print("\n✅ Demo completed successfully!")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
