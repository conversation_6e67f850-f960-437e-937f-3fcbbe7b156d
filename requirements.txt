# Core Computer Vision and Deep Learning
opencv-python==********
torch==2.0.1
torchvision==0.15.2
ultralytics==8.0.196
numpy==1.24.3
Pillow==10.0.0

# Image Processing and Analysis
scikit-image==0.21.0
matplotlib==3.7.2
seaborn==0.12.2

# Video Processing
imageio==2.31.1
imageio-ffmpeg==0.4.8

# Machine Learning and Data Science
scikit-learn==1.3.0
pandas==2.0.3
scipy==1.11.1

# Database
SQLAlchemy==2.0.19
sqlite3

# Web Framework and API
Flask==2.3.2
Flask-SocketIO==5.3.5
Flask-CORS==4.0.0
requests==2.31.0

# Real-time Processing
threading
queue
multiprocessing

# Configuration and Logging
PyYAML==6.0.1
python-dotenv==1.0.0
loguru==0.7.0

# Utilities
tqdm==4.65.0
argparse
datetime
json
os
sys
time
collections

# Optional: GPU acceleration
# tensorflow-gpu==2.13.0  # Uncomment if using TensorFlow models
# onnxruntime-gpu==1.15.1  # Uncomment for ONNX model acceleration

# Development and Testing
pytest==7.4.0
black==23.7.0
flake8==6.0.0

# Visualization and Monitoring
plotly==5.15.0
dash==2.11.2
dash-bootstrap-components==1.4.1

# License Plate Recognition
easyocr==1.7.0
pytesseract==0.3.10

# Additional utilities
imutils==0.5.4
