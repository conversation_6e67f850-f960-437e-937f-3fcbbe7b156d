@echo off
REM Quick run script for Traffic Surveillance System (Windows)

echo Starting Traffic Surveillance System...
echo.

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found
    echo Please run install.bat first
    pause
    exit /b 1
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Run the system
python main.py %*

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo System exited with error. Press any key to close...
    pause >nul
)
