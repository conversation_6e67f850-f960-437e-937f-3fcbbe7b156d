#!/bin/bash
# Quick run script for Traffic Surveillance System (Linux/macOS)

echo "Starting Traffic Surveillance System..."
echo

# Check if virtual environment exists
if [ ! -f "venv/bin/activate" ]; then
    echo "ERROR: Virtual environment not found"
    echo "Please run ./install.sh first"
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Run the system
python main.py "$@"
