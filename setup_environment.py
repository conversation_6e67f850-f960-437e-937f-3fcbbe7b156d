#!/usr/bin/env python3
"""
Setup Environment for Vehicle Detection
This script helps you install the required packages
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and show the result"""
    print(f"\n🔧 {description}...")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()[:200]}...")
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {str(e)}")
        return False
    
    return True

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required!")
        return False
    else:
        print("✅ Python version is compatible")
        return True

def install_packages():
    """Install required packages"""
    print("\n" + "="*60)
    print("📦 INSTALLING REQUIRED PACKAGES")
    print("="*60)
    
    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("❌ requirements.txt not found!")
        return False
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", 
                      "Upgrading pip"):
        print("⚠️  Pip upgrade failed, continuing anyway...")
    
    # Install basic packages first (most important ones)
    basic_packages = [
        "opencv-python",
        "numpy", 
        "Pillow",
        "matplotlib"
    ]
    
    print("\n📦 Installing basic packages first...")
    for package in basic_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", 
                          f"Installing {package}"):
            print(f"⚠️  Failed to install {package}, but continuing...")
    
    # Install from requirements.txt
    print("\n📦 Installing all packages from requirements.txt...")
    success = run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                         "Installing from requirements.txt")
    
    return success

def test_imports():
    """Test if key packages can be imported"""
    print("\n" + "="*60)
    print("🧪 TESTING PACKAGE IMPORTS")
    print("="*60)
    
    test_packages = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("matplotlib", "Matplotlib")
    ]
    
    all_good = True
    
    for package, name in test_packages:
        try:
            __import__(package)
            print(f"✅ {name} - OK")
        except ImportError as e:
            print(f"❌ {name} - FAILED: {str(e)}")
            all_good = False
    
    return all_good

def create_simple_test():
    """Create a simple test script that doesn't require complex models"""
    test_script = """#!/usr/bin/env python3
import cv2
import numpy as np
import json

def simple_test():
    print("🧪 Testing basic functionality...")
    
    # Test OpenCV
    print("📸 Testing OpenCV...")
    img = np.zeros((300, 400, 3), dtype=np.uint8)
    cv2.rectangle(img, (50, 50), (150, 100), (0, 255, 0), 2)
    cv2.putText(img, "TEST", (60, 80), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    # Save test image
    cv2.imwrite("test_image.jpg", img)
    print("✅ OpenCV working - test_image.jpg created")
    
    # Test JSON
    test_data = {
        "test": "success",
        "opencv_version": cv2.__version__,
        "numpy_version": np.__version__
    }
    
    with open("test_results.json", "w") as f:
        json.dump(test_data, f, indent=2)
    
    print("✅ JSON working - test_results.json created")
    
    # Display test image
    print("🖼️  Displaying test image (press any key to close)...")
    cv2.imshow("Test Image", img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    print("✅ All basic tests passed!")
    return True

if __name__ == "__main__":
    simple_test()
"""
    
    with open("simple_test.py", "w") as f:
        f.write(test_script)
    
    print("✅ Created simple_test.py")

def main():
    """Main setup function"""
    print("🚀 VEHICLE DETECTION ENVIRONMENT SETUP")
    print("="*60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install packages
    if not install_packages():
        print("\n❌ Package installation failed!")
        print("💡 Try running manually:")
        print("   pip install opencv-python numpy Pillow matplotlib")
        return 1
    
    # Test imports
    if not test_imports():
        print("\n❌ Some packages failed to import!")
        print("💡 Try installing manually:")
        print("   pip install opencv-python")
        return 1
    
    # Create simple test
    create_simple_test()
    
    print("\n" + "="*60)
    print("✅ SETUP COMPLETE!")
    print("="*60)
    
    print("\n🎯 Next steps:")
    print("1. Test basic functionality:")
    print("   python simple_test.py")
    print("\n2. Try the vehicle detection demo:")
    print("   python test_demo.py")
    print("\n3. Use with your own image:")
    print("   python quick_demo.py your_image.jpg")
    
    return 0

if __name__ == "__main__":
    exit(main())
