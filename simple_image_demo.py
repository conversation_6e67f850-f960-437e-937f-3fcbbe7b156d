#!/usr/bin/env python3
"""
Simple Image Vehicle Detection Demo
Input: Image file
Output: Vehicle count, types, and license plates detected

This is a simplified demo script that shows how to use the existing
traffic surveillance system for single image analysis.
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import argparse
import json
from typing import Dict, List, Any

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from src.models.vehicle_detector import VehicleDetector
    from src.models.license_plate_detector import LicensePlateDetector
except ImportError:
    print("⚠️  Detection models not found. Please ensure the src/models/ directory exists.")
    print("   This demo requires the vehicle detection and license plate recognition modules.")
    sys.exit(1)


def analyze_single_image(image_path: str, show_result: bool = True, save_result: str = None):
    """
    Analyze a single image for vehicles and license plates
    
    Args:
        image_path: Path to the input image
        show_result: Whether to display the result
        save_result: Path to save the annotated result image
    
    Returns:
        Dictionary with analysis results
    """
    print(f"🔍 Analyzing image: {image_path}")
    
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    print(f"📏 Image size: {image.shape[1]}x{image.shape[0]} pixels")
    
    # Initialize detectors
    print("🚗 Initializing vehicle detector...")
    vehicle_detector = VehicleDetector()
    
    print("🔢 Initializing license plate detector...")
    try:
        license_plate_detector = LicensePlateDetector()
        lp_available = True
    except Exception as e:
        print(f"⚠️  License plate detector not available: {e}")
        license_plate_detector = None
        lp_available = False
    
    # Detect vehicles
    print("🔍 Detecting vehicles...")
    vehicle_detections = vehicle_detector.detect_vehicles(image)
    
    # Detect license plates
    license_plate_detections = []
    if lp_available:
        print("🔍 Detecting license plates...")
        lp_detections = license_plate_detector.detect_license_plates(image)
        license_plate_detections = license_plate_detector.recognize_license_plates(
            image, lp_detections
        )
    
    # Count vehicles by type
    vehicle_counts = {}
    for detection in vehicle_detections:
        vehicle_type = detection['class_name']
        vehicle_counts[vehicle_type] = vehicle_counts.get(vehicle_type, 0) + 1
    
    # Process license plates
    readable_plates = []
    for plate_detection in license_plate_detections:
        plate_text = plate_detection.get('text', '').strip()
        if plate_text:
            readable_plates.append({
                'text': plate_text,
                'confidence': plate_detection['confidence']
            })
    
    # Create results summary
    results = {
        'image_info': {
            'filename': Path(image_path).name,
            'dimensions': f"{image.shape[1]}x{image.shape[0]}",
            'total_pixels': image.shape[0] * image.shape[1]
        },
        'detection_summary': {
            'total_vehicles': len(vehicle_detections),
            'vehicle_types_found': len(vehicle_counts),
            'license_plates_detected': len(license_plate_detections),
            'readable_license_plates': len(readable_plates)
        },
        'vehicle_breakdown': vehicle_counts,
        'license_plates': readable_plates,
        'detailed_detections': {
            'vehicles': [
                {
                    'type': det['class_name'],
                    'confidence': round(det['confidence'], 3),
                    'position': det.get('center', 'N/A')
                }
                for det in vehicle_detections
            ]
        }
    }
    
    # Draw annotations on image
    annotated_image = image.copy()
    
    # Draw vehicle detections
    annotated_image = vehicle_detector.draw_detections(annotated_image, vehicle_detections)
    
    # Draw license plate detections
    if lp_available and license_plate_detections:
        annotated_image = license_plate_detector.draw_license_plates(
            annotated_image, license_plate_detections
        )
    
    # Add summary text overlay
    summary_text = [
        f"Vehicles: {len(vehicle_detections)}",
        f"License Plates: {len(readable_plates)}"
    ]
    
    # Draw summary box
    for i, text in enumerate(summary_text):
        cv2.rectangle(annotated_image, (10, 10 + i * 35), (300, 40 + i * 35), (0, 0, 0), -1)
        cv2.putText(annotated_image, text, (20, 35 + i * 35), cv2.FONT_HERSHEY_SIMPLEX, 
                   0.8, (255, 255, 255), 2)
    
    # Save result image if requested
    if save_result:
        cv2.imwrite(save_result, annotated_image)
        print(f"💾 Annotated image saved to: {save_result}")
    
    # Display result if requested
    if show_result:
        # Resize image if too large for display
        height, width = annotated_image.shape[:2]
        if width > 1200 or height > 800:
            scale = min(1200/width, 800/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            display_image = cv2.resize(annotated_image, (new_width, new_height))
        else:
            display_image = annotated_image
        
        cv2.imshow('Vehicle Detection Results', display_image)
        print("\n📋 Press any key to close the image window...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    return results


def print_results(results: Dict[str, Any]):
    """Print analysis results in a formatted way"""
    
    print("\n" + "="*60)
    print("🚗 VEHICLE DETECTION RESULTS")
    print("="*60)
    
    # Image info
    image_info = results['image_info']
    print(f"📸 Image: {image_info['filename']}")
    print(f"📏 Dimensions: {image_info['dimensions']}")
    
    # Detection summary
    summary = results['detection_summary']
    print(f"\n📊 DETECTION SUMMARY:")
    print(f"   Total Vehicles: {summary['total_vehicles']}")
    print(f"   Vehicle Types: {summary['vehicle_types_found']}")
    print(f"   License Plates Detected: {summary['license_plates_detected']}")
    print(f"   Readable License Plates: {summary['readable_license_plates']}")
    
    # Vehicle breakdown
    print(f"\n🚙 VEHICLE TYPES:")
    vehicle_breakdown = results['vehicle_breakdown']
    if vehicle_breakdown:
        for vehicle_type, count in vehicle_breakdown.items():
            print(f"   {vehicle_type.capitalize()}: {count}")
    else:
        print("   No vehicles detected")
    
    # License plates
    print(f"\n🔢 LICENSE PLATES:")
    license_plates = results['license_plates']
    if license_plates:
        for i, plate in enumerate(license_plates, 1):
            print(f"   {i}. {plate['text']} (Confidence: {plate['confidence']:.3f})")
    else:
        print("   No readable license plates found")
    
    # Detailed vehicle list
    print(f"\n📋 DETAILED VEHICLE LIST:")
    vehicles = results['detailed_detections']['vehicles']
    if vehicles:
        for i, vehicle in enumerate(vehicles, 1):
            print(f"   {i}. {vehicle['type'].capitalize()} - "
                  f"Confidence: {vehicle['confidence']:.3f} - "
                  f"Position: {vehicle['position']}")
    else:
        print("   No vehicles detected")
    
    print("="*60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Simple vehicle detection demo for single images',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python simple_image_demo.py traffic.jpg
  python simple_image_demo.py image.png --save result.jpg --json results.json
  python simple_image_demo.py photo.jpg --no-display
        """
    )
    
    parser.add_argument('image', help='Path to input image file')
    parser.add_argument('--save', help='Path to save annotated result image')
    parser.add_argument('--json', help='Path to save results as JSON file')
    parser.add_argument('--no-display', action='store_true', 
                       help='Do not display the result image')
    
    args = parser.parse_args()
    
    # Check if input image exists
    if not Path(args.image).exists():
        print(f"❌ Error: Image file '{args.image}' not found!")
        return 1
    
    try:
        # Analyze the image
        results = analyze_single_image(
            args.image,
            show_result=not args.no_display,
            save_result=args.save
        )
        
        # Print results to console
        print_results(results)
        
        # Save JSON results if requested
        if args.json:
            with open(args.json, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"\n💾 Results saved to JSON: {args.json}")
        
        # Final summary
        summary = results['detection_summary']
        print(f"\n✅ Analysis complete!")
        print(f"   Found {summary['total_vehicles']} vehicles and "
              f"{summary['readable_license_plates']} readable license plates")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during analysis: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())
