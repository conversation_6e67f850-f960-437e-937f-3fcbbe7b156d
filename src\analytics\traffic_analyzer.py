"""
Traffic Analytics Module
Implements vehicle counting, speed estimation, flow analysis, and violation detection
"""

import cv2
import numpy as np
import math
from typing import Dict, List, Any, Tuple, Optional
from collections import defaultdict, deque
import time
import logging

logger = logging.getLogger(__name__)


class VehicleTracker:
    """Simple vehicle tracking for analytics"""
    
    def __init__(self, max_disappeared: int = 30):
        self.next_id = 0
        self.objects = {}
        self.disappeared = {}
        self.max_disappeared = max_disappeared
        
    def register(self, centroid: Tuple[int, int]) -> int:
        """Register a new object"""
        self.objects[self.next_id] = centroid
        self.disappeared[self.next_id] = 0
        self.next_id += 1
        return self.next_id - 1
    
    def deregister(self, object_id: int):
        """Deregister an object"""
        del self.objects[object_id]
        del self.disappeared[object_id]
    
    def update(self, detections: List[Dict]) -> Dict[int, Tuple[int, int]]:
        """Update tracker with new detections"""
        if len(detections) == 0:
            # Mark all existing objects as disappeared
            for object_id in list(self.disappeared.keys()):
                self.disappeared[object_id] += 1
                if self.disappeared[object_id] > self.max_disappeared:
                    self.deregister(object_id)
            return self.objects
        
        # Initialize centroids array
        input_centroids = np.array([det['center'] for det in detections])
        
        if len(self.objects) == 0:
            # Register all detections as new objects
            for centroid in input_centroids:
                self.register(tuple(centroid))
        else:
            # Get existing object centroids
            object_centroids = np.array(list(self.objects.values()))
            object_ids = list(self.objects.keys())
            
            # Compute distance matrix
            D = np.linalg.norm(object_centroids[:, np.newaxis] - input_centroids, axis=2)
            
            # Find minimum values and sort by distance
            rows = D.min(axis=1).argsort()
            cols = D.argmin(axis=1)[rows]
            
            # Keep track of used row and column indices
            used_rows = set()
            used_cols = set()
            
            # Update existing objects
            for (row, col) in zip(rows, cols):
                if row in used_rows or col in used_cols:
                    continue
                
                if D[row, col] > 50:  # Maximum distance threshold
                    continue
                
                object_id = object_ids[row]
                self.objects[object_id] = tuple(input_centroids[col])
                self.disappeared[object_id] = 0
                
                used_rows.add(row)
                used_cols.add(col)
            
            # Handle unmatched detections and objects
            unused_rows = set(range(0, D.shape[0])).difference(used_rows)
            unused_cols = set(range(0, D.shape[1])).difference(used_cols)
            
            if D.shape[0] >= D.shape[1]:
                # More existing objects than detections
                for row in unused_rows:
                    object_id = object_ids[row]
                    self.disappeared[object_id] += 1
                    
                    if self.disappeared[object_id] > self.max_disappeared:
                        self.deregister(object_id)
            else:
                # More detections than existing objects
                for col in unused_cols:
                    self.register(tuple(input_centroids[col]))
        
        return self.objects


class TrafficAnalyzer:
    """
    Comprehensive traffic analysis system
    Handles counting, speed estimation, flow analysis, and violation detection
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize traffic analyzer
        
        Args:
            config: Analytics configuration
        """
        self.config = config
        
        # Vehicle tracking
        self.tracker = VehicleTracker()
        self.vehicle_paths = defaultdict(deque)  # Store vehicle movement history
        
        # Counting lines
        self.counting_lines = self._setup_counting_lines()
        self.vehicle_counts = {'entry': 0, 'exit': 0}
        self.crossed_vehicles = set()  # Track which vehicles crossed lines
        
        # Speed estimation
        self.speed_config = config.get('speed_estimation', {})
        self.calibration_distance = self.speed_config.get('calibration_distance', 10)  # meters
        self.calibration_pixels = self.speed_config.get('calibration_pixels', 100)  # pixels
        self.pixels_per_meter = self.calibration_pixels / self.calibration_distance
        
        # Traffic flow analysis
        self.flow_history = deque(maxlen=300)  # 10 seconds at 30 FPS
        self.congestion_threshold = config.get('flow_analysis', {}).get('congestion_threshold', 0.7)
        
        # Violation detection
        self.violations_config = config.get('violations', {})
        self.speed_limit = self.violations_config.get('speed_limit', 60)  # km/h
        self.violations = []
        
        # Statistics
        self.total_vehicles = 0
        self.total_violations = 0
        self.start_time = time.time()
        
    def _setup_counting_lines(self) -> List[Dict]:
        """Setup counting lines from configuration"""
        lines = []
        counting_config = self.config.get('counting', {})
        
        if counting_config.get('enabled', False):
            counting_lines = counting_config.get('counting_lines', [])
            
            for line_config in counting_lines:
                line = {
                    'name': line_config['name'],
                    'coordinates': line_config['coordinates'],
                    'count': 0
                }
                lines.append(line)
        
        return lines
    
    def analyze_frame(self, frame: np.ndarray, detections: Dict[str, Any], 
                     timestamp: float) -> Dict[str, Any]:
        """
        Analyze frame for traffic metrics
        
        Args:
            frame: Input frame
            detections: Detection results
            timestamp: Frame timestamp
            
        Returns:
            Analytics results dictionary
        """
        vehicle_detections = detections.get('vehicles', [])
        
        # Update vehicle tracking
        tracked_objects = self.tracker.update(vehicle_detections)
        
        # Update vehicle paths for speed calculation
        self._update_vehicle_paths(tracked_objects, timestamp)
        
        # Vehicle counting
        counting_results = self._count_vehicles(tracked_objects)
        
        # Speed estimation
        speed_results = self._estimate_speeds(timestamp)
        
        # Traffic flow analysis
        flow_results = self._analyze_traffic_flow(len(vehicle_detections))
        
        # Violation detection
        violation_results = self._detect_violations(speed_results, detections)
        
        # Compile results
        analytics_results = {
            'vehicle_count': len(vehicle_detections),
            'tracked_vehicles': len(tracked_objects),
            'counting': counting_results,
            'speeds': speed_results,
            'average_speed': np.mean([s['speed'] for s in speed_results]) if speed_results else 0,
            'traffic_flow': flow_results,
            'violations': violation_results,
            'violations_count': len(violation_results),
            'timestamp': timestamp
        }
        
        return analytics_results
    
    def _update_vehicle_paths(self, tracked_objects: Dict[int, Tuple[int, int]], 
                            timestamp: float):
        """Update vehicle movement paths"""
        for vehicle_id, position in tracked_objects.items():
            self.vehicle_paths[vehicle_id].append({
                'position': position,
                'timestamp': timestamp
            })
            
            # Keep only recent positions (last 5 seconds)
            while (len(self.vehicle_paths[vehicle_id]) > 0 and 
                   timestamp - self.vehicle_paths[vehicle_id][0]['timestamp'] > 5.0):
                self.vehicle_paths[vehicle_id].popleft()
    
    def _count_vehicles(self, tracked_objects: Dict[int, Tuple[int, int]]) -> Dict[str, Any]:
        """Count vehicles crossing counting lines"""
        results = {
            'total_entry': self.vehicle_counts['entry'],
            'total_exit': self.vehicle_counts['exit'],
            'current_vehicles': len(tracked_objects),
            'line_crossings': []
        }
        
        for line in self.counting_lines:
            line_crossings = 0
            
            for vehicle_id, position in tracked_objects.items():
                # Check if vehicle crossed this line
                if self._check_line_crossing(vehicle_id, position, line):
                    line_crossings += 1
                    line['count'] += 1
                    
                    # Update global counts based on line name
                    if 'entry' in line['name'].lower():
                        self.vehicle_counts['entry'] += 1
                    elif 'exit' in line['name'].lower():
                        self.vehicle_counts['exit'] += 1
            
            results['line_crossings'].append({
                'line_name': line['name'],
                'crossings': line_crossings,
                'total_crossings': line['count']
            })
        
        return results
    
    def _check_line_crossing(self, vehicle_id: int, current_position: Tuple[int, int], 
                           line: Dict) -> bool:
        """Check if vehicle crossed a counting line"""
        if vehicle_id not in self.vehicle_paths or len(self.vehicle_paths[vehicle_id]) < 2:
            return False
        
        # Get previous position
        previous_position = self.vehicle_paths[vehicle_id][-2]['position']
        
        # Check if line was crossed between previous and current position
        line_coords = line['coordinates']
        if len(line_coords) != 2:
            return False
        
        line_start = tuple(line_coords[0])
        line_end = tuple(line_coords[1])
        
        # Use line intersection algorithm
        crossed = self._line_intersection(
            previous_position, current_position, line_start, line_end
        )
        
        # Avoid counting the same vehicle multiple times
        crossing_key = f"{vehicle_id}_{line['name']}"
        if crossed and crossing_key not in self.crossed_vehicles:
            self.crossed_vehicles.add(crossing_key)
            return True
        
        return False
    
    def _line_intersection(self, p1: Tuple[int, int], p2: Tuple[int, int], 
                          p3: Tuple[int, int], p4: Tuple[int, int]) -> bool:
        """Check if two line segments intersect"""
        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])
        
        return ccw(p1, p3, p4) != ccw(p2, p3, p4) and ccw(p1, p2, p3) != ccw(p1, p2, p4)
    
    def _estimate_speeds(self, current_timestamp: float) -> List[Dict[str, Any]]:
        """Estimate vehicle speeds"""
        speeds = []
        
        if not self.speed_config.get('enabled', False):
            return speeds
        
        for vehicle_id, path in self.vehicle_paths.items():
            if len(path) < 2:
                continue
            
            # Calculate speed using recent positions
            recent_positions = list(path)[-10:]  # Last 10 positions
            
            if len(recent_positions) < 2:
                continue
            
            # Calculate distance and time
            start_pos = recent_positions[0]['position']
            end_pos = recent_positions[-1]['position']
            
            distance_pixels = math.sqrt(
                (end_pos[0] - start_pos[0]) ** 2 + (end_pos[1] - start_pos[1]) ** 2
            )
            
            time_diff = recent_positions[-1]['timestamp'] - recent_positions[0]['timestamp']
            
            if time_diff > 0:
                # Convert to real-world units
                distance_meters = distance_pixels / self.pixels_per_meter
                speed_mps = distance_meters / time_diff
                speed_kmh = speed_mps * 3.6
                
                speeds.append({
                    'vehicle_id': vehicle_id,
                    'speed': speed_kmh,
                    'position': end_pos,
                    'timestamp': current_timestamp
                })
        
        return speeds
    
    def _analyze_traffic_flow(self, current_vehicle_count: int) -> Dict[str, Any]:
        """Analyze traffic flow and congestion"""
        self.flow_history.append(current_vehicle_count)
        
        if len(self.flow_history) < 30:  # Need at least 1 second of data
            return {'status': 'Initializing', 'density': 0, 'congestion_level': 0}
        
        # Calculate average density
        avg_density = np.mean(list(self.flow_history))
        max_density = max(self.flow_history) if self.flow_history else 1
        
        # Normalize congestion level (0-1)
        congestion_level = min(avg_density / max(max_density, 1), 1.0)
        
        # Determine flow status
        if congestion_level < 0.3:
            status = 'Light'
        elif congestion_level < self.congestion_threshold:
            status = 'Moderate'
        else:
            status = 'Heavy'
        
        return {
            'status': status,
            'density': avg_density,
            'congestion_level': congestion_level,
            'vehicle_count_trend': list(self.flow_history)[-10:]  # Last 10 readings
        }
    
    def _detect_violations(self, speed_results: List[Dict], 
                          detections: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect traffic violations"""
        violations = []
        
        # Speed violations
        for speed_data in speed_results:
            if speed_data['speed'] > self.speed_limit:
                violation = {
                    'type': 'speed_violation',
                    'vehicle_id': speed_data['vehicle_id'],
                    'speed': speed_data['speed'],
                    'speed_limit': self.speed_limit,
                    'position': speed_data['position'],
                    'timestamp': speed_data['timestamp'],
                    'severity': 'high' if speed_data['speed'] > self.speed_limit * 1.5 else 'medium'
                }
                violations.append(violation)
        
        # Red light violations (if traffic light detection is available)
        traffic_signs = detections.get('traffic_signs', [])
        red_lights = [sign for sign in traffic_signs 
                     if sign.get('class_name') == 'traffic_light' and sign.get('state') == 'red']
        
        if red_lights and self.violations_config.get('red_light_violation', False):
            # Check if vehicles are moving through red light areas
            vehicles = detections.get('vehicles', [])
            for red_light in red_lights:
                light_area = self._get_traffic_light_area(red_light)
                for vehicle in vehicles:
                    if self._is_vehicle_in_area(vehicle, light_area):
                        violation = {
                            'type': 'red_light_violation',
                            'vehicle_position': vehicle['center'],
                            'light_position': red_light['center'],
                            'timestamp': detections['timestamp'],
                            'severity': 'high'
                        }
                        violations.append(violation)
        
        # Update violation statistics
        self.total_violations += len(violations)
        self.violations.extend(violations)
        
        return violations
    
    def _get_traffic_light_area(self, traffic_light: Dict) -> Dict:
        """Get area influenced by traffic light"""
        x1, y1, x2, y2 = traffic_light['bbox']
        
        # Extend area below traffic light (typical intersection area)
        area = {
            'x1': x1 - 50,
            'y1': y2,
            'x2': x2 + 50,
            'y2': y2 + 200
        }
        
        return area
    
    def _is_vehicle_in_area(self, vehicle: Dict, area: Dict) -> bool:
        """Check if vehicle is in specified area"""
        vx, vy = vehicle['center']
        return (area['x1'] <= vx <= area['x2'] and 
                area['y1'] <= vy <= area['y2'])
    
    def draw_analytics(self, frame: np.ndarray, analytics: Dict[str, Any]) -> np.ndarray:
        """
        Draw analytics overlays on frame
        
        Args:
            frame: Input frame
            analytics: Analytics results
            
        Returns:
            Frame with analytics overlays
        """
        # Draw counting lines
        for line in self.counting_lines:
            coords = line['coordinates']
            if len(coords) == 2:
                pt1 = tuple(coords[0])
                pt2 = tuple(coords[1])
                cv2.line(frame, pt1, pt2, (0, 255, 255), 3)
                
                # Draw line label
                label_pos = (pt1[0], pt1[1] - 10)
                cv2.putText(frame, f"{line['name']}: {line['count']}", 
                           label_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # Draw speed information
        speeds = analytics.get('speeds', [])
        for speed_data in speeds:
            pos = speed_data['position']
            speed = speed_data['speed']
            
            # Color based on speed limit
            color = (0, 255, 0) if speed <= self.speed_limit else (0, 0, 255)
            
            cv2.putText(frame, f"{speed:.1f} km/h", 
                       (pos[0] - 30, pos[1] - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        # Draw violations
        violations = analytics.get('violations', [])
        for violation in violations:
            if 'position' in violation:
                pos = violation['position']
                cv2.circle(frame, pos, 20, (0, 0, 255), 3)
                cv2.putText(frame, "VIOLATION", 
                           (pos[0] - 40, pos[1] + 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        return frame
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get analytics statistics"""
        uptime = time.time() - self.start_time
        
        return {
            'uptime': uptime,
            'total_vehicles_counted': sum(self.vehicle_counts.values()),
            'entry_count': self.vehicle_counts['entry'],
            'exit_count': self.vehicle_counts['exit'],
            'total_violations': self.total_violations,
            'violations_per_hour': (self.total_violations / max(uptime / 3600, 1)),
            'counting_lines': len(self.counting_lines),
            'speed_limit': self.speed_limit
        }


# Example usage
if __name__ == "__main__":
    # Test configuration
    config = {
        'speed_estimation': {
            'enabled': True,
            'calibration_distance': 10,
            'calibration_pixels': 100
        },
        'counting': {
            'enabled': True,
            'counting_lines': [
                {
                    'name': 'Entry Line',
                    'coordinates': [[100, 300], [500, 300]]
                }
            ]
        },
        'violations': {
            'speed_limit': 50,
            'red_light_violation': True
        }
    }
    
    analyzer = TrafficAnalyzer(config)
    print("Traffic analyzer initialized successfully")
