"""
Main Traffic Surveillance System
Integrates all components for real-time traffic monitoring
"""

import cv2
import numpy as np
import threading
import time
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path

from .video_processor import VideoProcessor, MultiSourceProcessor
from ..models.vehicle_detector import VehicleDetector
from ..models.license_plate_detector import LicensePlateDetector
from ..models.traffic_sign_detector import TrafficSignDetector
from ..analytics.traffic_analyzer import TrafficAnalyzer
from ..utils.database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class TrafficSurveillanceSystem:
    """
    Main traffic surveillance system that coordinates all components
    """
    
    def __init__(self, config: Dict[str, Any], output_dir: Optional[str] = None, 
                 headless: bool = False):
        """
        Initialize traffic surveillance system
        
        Args:
            config: System configuration
            output_dir: Output directory for results
            headless: Run without video display
        """
        self.config = config
        self.output_dir = output_dir
        self.headless = headless
        
        # Initialize components
        self.video_processor = None
        self.vehicle_detector = None
        self.license_plate_detector = None
        self.traffic_sign_detector = None
        self.traffic_analyzer = None
        self.database_manager = None
        
        # System state
        self.running = False
        self.start_time = None
        
        # Results storage
        self.current_detections = {}
        self.analytics_data = {}
        
        # Initialize all components
        self._initialize_components()
        
    def _initialize_components(self):
        """Initialize all system components"""
        try:
            logger.info("Initializing traffic surveillance system components...")
            
            # Initialize detectors
            vehicle_config = self.config['models']['vehicle_detection']
            self.vehicle_detector = VehicleDetector(
                model_path=vehicle_config['model_path'],
                device=vehicle_config['device'],
                confidence_threshold=vehicle_config['confidence_threshold'],
                iou_threshold=vehicle_config['iou_threshold']
            )
            
            # Initialize license plate detector if enabled
            if self.config['models'].get('license_plate', {}).get('model_path'):
                lp_config = self.config['models']['license_plate']
                self.license_plate_detector = LicensePlateDetector(
                    model_path=lp_config['model_path'],
                    device=vehicle_config['device'],
                    confidence_threshold=lp_config['confidence_threshold'],
                    ocr_engine=lp_config['ocr_engine']
                )
            
            # Initialize traffic sign detector if enabled
            if self.config['models'].get('traffic_signs', {}).get('model_path'):
                ts_config = self.config['models']['traffic_signs']
                self.traffic_sign_detector = TrafficSignDetector(
                    model_path=ts_config['model_path'],
                    device=vehicle_config['device'],
                    confidence_threshold=ts_config['confidence_threshold']
                )
            
            # Initialize traffic analyzer
            self.traffic_analyzer = TrafficAnalyzer(self.config['analytics'])
            
            # Initialize database manager
            self.database_manager = DatabaseManager(self.config['database'])
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {str(e)}")
            raise
    
    def _setup_video_processor(self):
        """Setup video processor for the first video source"""
        sources = self.config['video']['sources']
        if not sources:
            raise ValueError("No video sources configured")
        
        # Use first source for now (can be extended for multiple sources)
        source = sources[0]
        
        self.video_processor = VideoProcessor(source, self.config['video'])
        self.video_processor.set_frame_callback(self._process_frame)
        
        logger.info(f"Video processor setup for source: {source}")
    
    def _process_frame(self, frame_data: Dict[str, Any]):
        """
        Process a single frame through the detection pipeline
        
        Args:
            frame_data: Frame data from video processor
        """
        try:
            frame = frame_data['frame']
            timestamp = frame_data['timestamp']
            frame_id = frame_data['frame_id']
            
            # Vehicle detection
            vehicle_detections = self.vehicle_detector.detect_vehicles(frame)
            
            # License plate detection (if enabled)
            license_plate_detections = []
            if self.license_plate_detector:
                lp_detections = self.license_plate_detector.detect_license_plates(frame)
                license_plate_detections = self.license_plate_detector.recognize_license_plates(
                    frame, lp_detections
                )
            
            # Traffic sign detection (if enabled)
            traffic_sign_detections = []
            if self.traffic_sign_detector:
                traffic_sign_detections = self.traffic_sign_detector.detect_traffic_signs(frame)
                
                # Also detect traffic lights
                traffic_light_detections = self.traffic_sign_detector.detect_traffic_lights(frame)
                traffic_sign_detections.extend(traffic_light_detections)
            
            # Combine all detections
            all_detections = {
                'vehicles': vehicle_detections,
                'license_plates': license_plate_detections,
                'traffic_signs': traffic_sign_detections,
                'timestamp': timestamp,
                'frame_id': frame_id
            }
            
            # Traffic analysis
            analytics_result = self.traffic_analyzer.analyze_frame(
                frame, all_detections, timestamp
            )
            
            # Store current detections
            self.current_detections = all_detections
            self.analytics_data = analytics_result
            
            # Save to database
            if self.database_manager:
                self._save_detections_to_database(all_detections, analytics_result)
            
            # Draw visualizations if not headless
            if not self.headless:
                self._draw_visualizations(frame, all_detections, analytics_result)
            
        except Exception as e:
            logger.error(f"Error processing frame {frame_data.get('frame_id', 'unknown')}: {str(e)}")
    
    def _draw_visualizations(self, frame: np.ndarray, detections: Dict[str, Any], 
                           analytics: Dict[str, Any]):
        """
        Draw all visualizations on frame
        
        Args:
            frame: Input frame
            detections: All detection results
            analytics: Analytics results
        """
        # Draw vehicle detections
        if detections['vehicles']:
            frame = self.vehicle_detector.draw_detections(frame, detections['vehicles'])
        
        # Draw license plate detections
        if detections['license_plates'] and self.license_plate_detector:
            frame = self.license_plate_detector.draw_license_plates(
                frame, detections['license_plates']
            )
        
        # Draw traffic sign detections
        if detections['traffic_signs'] and self.traffic_sign_detector:
            frame = self.traffic_sign_detector.draw_traffic_signs(
                frame, detections['traffic_signs']
            )
        
        # Draw analytics overlays
        frame = self.traffic_analyzer.draw_analytics(frame, analytics)
        
        # Draw system information
        self._draw_system_info(frame, analytics)
        
        # Display frame
        cv2.imshow('Traffic Surveillance', frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            self.stop()
        elif key == ord('s'):
            # Save screenshot
            timestamp = int(time.time())
            filename = f"screenshot_{timestamp}.jpg"
            if self.output_dir:
                filepath = Path(self.output_dir) / filename
            else:
                filepath = Path(filename)
            cv2.imwrite(str(filepath), frame)
            logger.info(f"Screenshot saved: {filepath}")
    
    def _draw_system_info(self, frame: np.ndarray, analytics: Dict[str, Any]):
        """Draw system information overlay"""
        # System stats
        stats = self.get_system_statistics()
        
        # Prepare info text
        info_lines = [
            f"FPS: {stats['processing_fps']:.1f}",
            f"Vehicles: {len(self.current_detections.get('vehicles', []))}",
            f"Speed Avg: {analytics.get('average_speed', 0):.1f} km/h",
            f"Flow: {analytics.get('traffic_flow', 'Normal')}",
            f"Violations: {analytics.get('violations_count', 0)}"
        ]
        
        # Draw background
        info_height = len(info_lines) * 25 + 20
        cv2.rectangle(frame, (10, 10), (300, info_height), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (300, info_height), (255, 255, 255), 2)
        
        # Draw text
        for i, line in enumerate(info_lines):
            y = 35 + i * 25
            cv2.putText(frame, line, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    def _save_detections_to_database(self, detections: Dict[str, Any], 
                                   analytics: Dict[str, Any]):
        """Save detection results to database"""
        try:
            # Save vehicle detections
            for vehicle in detections['vehicles']:
                self.database_manager.save_vehicle_detection(vehicle, detections['timestamp'])
            
            # Save license plate detections
            for plate in detections['license_plates']:
                if plate.get('text'):
                    self.database_manager.save_license_plate(plate, detections['timestamp'])
            
            # Save traffic violations
            violations = analytics.get('violations', [])
            for violation in violations:
                self.database_manager.save_violation(violation, detections['timestamp'])
            
            # Save analytics data
            self.database_manager.save_analytics(analytics, detections['timestamp'])
            
        except Exception as e:
            logger.error(f"Error saving to database: {str(e)}")
    
    def run(self):
        """Run the traffic surveillance system"""
        try:
            logger.info("Starting traffic surveillance system...")
            
            # Setup video processor
            self._setup_video_processor()
            
            # Start video processing
            if not self.video_processor.start():
                raise RuntimeError("Failed to start video processor")
            
            self.running = True
            self.start_time = time.time()
            
            logger.info("Traffic surveillance system started successfully")
            
            # Main loop
            while self.running:
                time.sleep(0.1)  # Small delay to prevent high CPU usage
                
                # Check if video processor is still running
                if not self.video_processor.is_running():
                    logger.info("Video processor stopped, ending surveillance")
                    break
            
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        except Exception as e:
            logger.error(f"Error in surveillance system: {str(e)}")
        finally:
            self.stop()
    
    def stop(self):
        """Stop the surveillance system"""
        if not self.running:
            return
        
        logger.info("Stopping traffic surveillance system...")
        self.running = False
        
        # Stop video processor
        if self.video_processor:
            self.video_processor.stop()
        
        # Close database connection
        if self.database_manager:
            self.database_manager.close()
        
        # Close OpenCV windows
        cv2.destroyAllWindows()
        
        logger.info("Traffic surveillance system stopped")
    
    def get_current_detections(self) -> Dict[str, Any]:
        """Get current detection results"""
        return self.current_detections.copy()
    
    def get_analytics_data(self) -> Dict[str, Any]:
        """Get current analytics data"""
        return self.analytics_data.copy()
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get system performance statistics"""
        stats = {
            'running': self.running,
            'uptime': time.time() - (self.start_time or time.time()),
            'processing_fps': 0,
            'total_vehicles_detected': 0,
            'total_violations': 0
        }
        
        # Add video processor stats
        if self.video_processor:
            video_stats = self.video_processor.get_statistics()
            stats.update({
                'processing_fps': video_stats['processing_fps'],
                'frames_processed': video_stats['frames_processed'],
                'frames_dropped': video_stats['frames_dropped']
            })
        
        # Add detector stats
        if self.vehicle_detector:
            vehicle_stats = self.vehicle_detector.get_statistics()
            stats['total_vehicles_detected'] = vehicle_stats['total_detections']
        
        # Add analyzer stats
        if self.traffic_analyzer:
            analyzer_stats = self.traffic_analyzer.get_statistics()
            stats['total_violations'] = analyzer_stats.get('total_violations', 0)
        
        return stats


# Example usage
if __name__ == "__main__":
    # Example configuration
    config = {
        'video': {
            'sources': ['0'],  # Webcam
            'fps': 30,
            'resolution': {'width': 640, 'height': 480},
            'skip_frames': 1,
            'buffer_size': 5
        },
        'models': {
            'vehicle_detection': {
                'model_path': 'yolov8n.pt',
                'device': 'cpu',
                'confidence_threshold': 0.5,
                'iou_threshold': 0.45
            }
        },
        'analytics': {
            'speed_estimation': {'enabled': True},
            'counting': {'enabled': True},
            'violations': {'speed_limit': 60}
        },
        'database': {
            'type': 'sqlite',
            'path': 'traffic_surveillance.db'
        }
    }
    
    # Create and run system
    system = TrafficSurveillanceSystem(config)
    
    try:
        system.run()
    except KeyboardInterrupt:
        print("Stopping system...")
        system.stop()
