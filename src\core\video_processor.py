"""
Real-time Video Processing Engine
Handles video stream capture, preprocessing, and frame management
"""

import cv2
import numpy as np
import threading
import queue
import time
from typing import Optional, Callable, Dict, Any, List
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class VideoProcessor:
    """
    Real-time video processing engine with multi-threading support
    Handles video capture, frame buffering, and preprocessing
    """
    
    def __init__(self, source: str, config: Dict[str, Any]):
        """
        Initialize video processor
        
        Args:
            source: Video source (file path, camera index, or RTSP URL)
            config: Configuration dictionary
        """
        self.source = source
        self.config = config
        
        # Video capture settings
        self.fps = config.get('fps', 30)
        self.resolution = config.get('resolution', {'width': 1920, 'height': 1080})
        self.skip_frames = config.get('skip_frames', 1)
        self.buffer_size = config.get('buffer_size', 10)
        
        # Video capture object
        self.cap = None
        self.is_camera = self._is_camera_source(source)
        
        # Frame processing
        self.frame_queue = queue.Queue(maxsize=self.buffer_size)
        self.processed_queue = queue.Queue(maxsize=self.buffer_size)
        
        # Threading
        self.capture_thread = None
        self.processing_thread = None
        self.running = False
        
        # Statistics
        self.frames_captured = 0
        self.frames_processed = 0
        self.frames_dropped = 0
        self.start_time = None
        
        # Callbacks
        self.frame_callback = None
        self.error_callback = None
        
    def _is_camera_source(self, source: str) -> bool:
        """Check if source is a camera (integer index)"""
        try:
            int(source)
            return True
        except ValueError:
            return False
    
    def initialize_capture(self) -> bool:
        """
        Initialize video capture
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert camera index to integer if needed
            if self.is_camera:
                source = int(self.source)
            else:
                source = self.source
            
            self.cap = cv2.VideoCapture(source)
            
            if not self.cap.isOpened():
                logger.error(f"Failed to open video source: {self.source}")
                return False
            
            # Set camera properties if it's a camera
            if self.is_camera:
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.resolution['width'])
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.resolution['height'])
                self.cap.set(cv2.CAP_PROP_FPS, self.fps)
                
                # Set buffer size to reduce latency
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            
            # Get actual properties
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            logger.info(f"Video source initialized: {self.source}")
            logger.info(f"Resolution: {actual_width}x{actual_height}")
            logger.info(f"FPS: {actual_fps}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing video capture: {str(e)}")
            return False
    
    def _capture_frames(self):
        """Capture frames in a separate thread"""
        frame_count = 0
        
        while self.running:
            try:
                ret, frame = self.cap.read()
                
                if not ret:
                    if not self.is_camera:  # End of video file
                        logger.info("End of video file reached")
                        break
                    else:
                        logger.warning("Failed to read frame from camera")
                        time.sleep(0.1)
                        continue
                
                frame_count += 1
                
                # Skip frames if configured
                if frame_count % (self.skip_frames + 1) != 0:
                    continue
                
                # Add timestamp to frame
                timestamp = time.time()
                frame_data = {
                    'frame': frame,
                    'timestamp': timestamp,
                    'frame_id': self.frames_captured
                }
                
                # Add to queue (non-blocking)
                try:
                    self.frame_queue.put_nowait(frame_data)
                    self.frames_captured += 1
                except queue.Full:
                    # Drop oldest frame if queue is full
                    try:
                        self.frame_queue.get_nowait()
                        self.frame_queue.put_nowait(frame_data)
                        self.frames_dropped += 1
                    except queue.Empty:
                        pass
                
            except Exception as e:
                logger.error(f"Error in frame capture: {str(e)}")
                if self.error_callback:
                    self.error_callback(e)
                break
        
        logger.info("Frame capture thread stopped")
    
    def _process_frames(self):
        """Process frames in a separate thread"""
        while self.running:
            try:
                # Get frame from queue (with timeout)
                frame_data = self.frame_queue.get(timeout=1.0)
                
                # Preprocess frame
                processed_frame = self.preprocess_frame(frame_data['frame'])
                
                # Update frame data
                frame_data['processed_frame'] = processed_frame
                frame_data['processing_time'] = time.time()
                
                # Call frame callback if set
                if self.frame_callback:
                    self.frame_callback(frame_data)
                
                # Add to processed queue
                try:
                    self.processed_queue.put_nowait(frame_data)
                    self.frames_processed += 1
                except queue.Full:
                    # Drop oldest processed frame
                    try:
                        self.processed_queue.get_nowait()
                        self.processed_queue.put_nowait(frame_data)
                    except queue.Empty:
                        pass
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in frame processing: {str(e)}")
                if self.error_callback:
                    self.error_callback(e)
        
        logger.info("Frame processing thread stopped")
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Preprocess frame for detection
        
        Args:
            frame: Input frame
            
        Returns:
            Preprocessed frame
        """
        # Resize if needed
        target_width = self.resolution.get('width', frame.shape[1])
        target_height = self.resolution.get('height', frame.shape[0])
        
        if frame.shape[1] != target_width or frame.shape[0] != target_height:
            frame = cv2.resize(frame, (target_width, target_height))
        
        # Additional preprocessing can be added here
        # - Noise reduction
        # - Brightness/contrast adjustment
        # - Color space conversion
        
        return frame
    
    def start(self) -> bool:
        """
        Start video processing
        
        Returns:
            True if started successfully, False otherwise
        """
        if self.running:
            logger.warning("Video processor is already running")
            return True
        
        if not self.initialize_capture():
            return False
        
        self.running = True
        self.start_time = time.time()
        
        # Start capture thread
        self.capture_thread = threading.Thread(target=self._capture_frames, daemon=True)
        self.capture_thread.start()
        
        # Start processing thread
        self.processing_thread = threading.Thread(target=self._process_frames, daemon=True)
        self.processing_thread.start()
        
        logger.info("Video processor started")
        return True
    
    def stop(self):
        """Stop video processing"""
        if not self.running:
            return
        
        logger.info("Stopping video processor...")
        self.running = False
        
        # Wait for threads to finish
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)
        
        # Release video capture
        if self.cap:
            self.cap.release()
        
        logger.info("Video processor stopped")
    
    def get_frame(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Get processed frame
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            Frame data dictionary or None
        """
        try:
            return self.processed_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def set_frame_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set callback function for processed frames"""
        self.frame_callback = callback
    
    def set_error_callback(self, callback: Callable[[Exception], None]):
        """Set callback function for errors"""
        self.error_callback = callback
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        current_time = time.time()
        elapsed_time = current_time - (self.start_time or current_time)
        
        capture_fps = self.frames_captured / max(elapsed_time, 1)
        processing_fps = self.frames_processed / max(elapsed_time, 1)
        
        return {
            'source': self.source,
            'is_camera': self.is_camera,
            'running': self.running,
            'elapsed_time': elapsed_time,
            'frames_captured': self.frames_captured,
            'frames_processed': self.frames_processed,
            'frames_dropped': self.frames_dropped,
            'capture_fps': capture_fps,
            'processing_fps': processing_fps,
            'frame_queue_size': self.frame_queue.qsize(),
            'processed_queue_size': self.processed_queue.qsize()
        }
    
    def is_running(self) -> bool:
        """Check if processor is running"""
        return self.running
    
    def __enter__(self):
        """Context manager entry"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop()


class MultiSourceProcessor:
    """
    Process multiple video sources simultaneously
    """
    
    def __init__(self, sources: List[str], config: Dict[str, Any]):
        """
        Initialize multi-source processor
        
        Args:
            sources: List of video sources
            config: Configuration dictionary
        """
        self.sources = sources
        self.config = config
        self.processors = {}
        self.running = False
        
    def start_all(self) -> Dict[str, bool]:
        """
        Start all processors
        
        Returns:
            Dictionary mapping source to success status
        """
        results = {}
        
        for source in self.sources:
            processor = VideoProcessor(source, self.config)
            success = processor.start()
            
            if success:
                self.processors[source] = processor
                logger.info(f"Started processor for source: {source}")
            else:
                logger.error(f"Failed to start processor for source: {source}")
            
            results[source] = success
        
        self.running = len(self.processors) > 0
        return results
    
    def stop_all(self):
        """Stop all processors"""
        for source, processor in self.processors.items():
            processor.stop()
            logger.info(f"Stopped processor for source: {source}")
        
        self.processors.clear()
        self.running = False
    
    def get_frame_from_source(self, source: str, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """Get frame from specific source"""
        processor = self.processors.get(source)
        if processor:
            return processor.get_frame(timeout)
        return None
    
    def get_all_frames(self, timeout: float = 1.0) -> Dict[str, Optional[Dict[str, Any]]]:
        """Get frames from all sources"""
        frames = {}
        for source, processor in self.processors.items():
            frames[source] = processor.get_frame(timeout)
        return frames
    
    def get_statistics(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all processors"""
        stats = {}
        for source, processor in self.processors.items():
            stats[source] = processor.get_statistics()
        return stats


# Example usage
if __name__ == "__main__":
    # Test with webcam
    config = {
        'fps': 30,
        'resolution': {'width': 640, 'height': 480},
        'skip_frames': 1,
        'buffer_size': 5
    }
    
    def frame_callback(frame_data):
        print(f"Processed frame {frame_data['frame_id']} at {frame_data['timestamp']}")
    
    processor = VideoProcessor("0", config)
    processor.set_frame_callback(frame_callback)
    
    if processor.start():
        try:
            while True:
                frame_data = processor.get_frame()
                if frame_data:
                    cv2.imshow('Video', frame_data['frame'])
                    
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # Print statistics every 100 frames
                stats = processor.get_statistics()
                if stats['frames_processed'] % 100 == 0 and stats['frames_processed'] > 0:
                    print(f"Stats: {stats}")
        
        finally:
            processor.stop()
            cv2.destroyAllWindows()
    else:
        print("Failed to start video processor")
