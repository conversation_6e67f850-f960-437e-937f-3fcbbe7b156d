"""
License Plate Detection and Recognition Module
Combines YOLO detection with OCR for license plate reading
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO
import easyocr
import pytesseract
import re
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class LicensePlateDetector:
    """
    License plate detection and OCR recognition system
    Uses YOLOv8 for detection and EasyOCR/Tesseract for text recognition
    """
    
    def __init__(self, model_path: str = "models/license_plate_detector.pt",
                 device: str = "cuda", confidence_threshold: float = 0.7,
                 ocr_engine: str = "easyocr"):
        """
        Initialize license plate detector
        
        Args:
            model_path: Path to license plate detection model
            device: Device for inference ('cuda' or 'cpu')
            confidence_threshold: Minimum confidence for detections
            ocr_engine: OCR engine to use ('easyocr' or 'tesseract')
        """
        self.model_path = model_path
        self.device = device
        self.confidence_threshold = confidence_threshold
        self.ocr_engine = ocr_engine
        
        # Initialize detection model
        self.model = None
        self.load_model()
        
        # Initialize OCR engine
        self.ocr_reader = None
        self.init_ocr()
        
        # Statistics
        self.total_detections = 0
        self.successful_reads = 0
        
    def load_model(self):
        """Load the license plate detection model"""
        try:
            logger.info(f"Loading license plate detection model from {self.model_path}")
            
            # Try to load custom model, fallback to YOLOv8 if not available
            try:
                self.model = YOLO(self.model_path)
            except:
                logger.warning("Custom license plate model not found, using YOLOv8n")
                self.model = YOLO("yolov8n.pt")
            
            # Move to device
            if self.device == "cuda" and torch.cuda.is_available():
                self.model.to("cuda")
                logger.info("License plate model loaded on GPU")
            else:
                self.model.to("cpu")
                logger.info("License plate model loaded on CPU")
                
        except Exception as e:
            logger.error(f"Failed to load license plate model: {str(e)}")
            raise
    
    def init_ocr(self):
        """Initialize OCR engine"""
        try:
            if self.ocr_engine == "easyocr":
                logger.info("Initializing EasyOCR...")
                self.ocr_reader = easyocr.Reader(['en'], gpu=torch.cuda.is_available())
                logger.info("EasyOCR initialized successfully")
            elif self.ocr_engine == "tesseract":
                logger.info("Using Tesseract OCR")
                # Test tesseract availability
                try:
                    pytesseract.get_tesseract_version()
                    logger.info("Tesseract OCR available")
                except:
                    logger.error("Tesseract not found. Please install tesseract-ocr")
                    raise
            else:
                raise ValueError(f"Unsupported OCR engine: {self.ocr_engine}")
                
        except Exception as e:
            logger.error(f"Failed to initialize OCR: {str(e)}")
            raise
    
    def detect_license_plates(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect license plates in frame
        
        Args:
            frame: Input image frame
            
        Returns:
            List of license plate detections with bounding boxes
        """
        if self.model is None:
            return []
        
        try:
            # Run detection
            results = self.model(
                frame,
                conf=self.confidence_threshold,
                verbose=False
            )
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        
                        detection = {
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence),
                            'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        }
                        
                        detections.append(detection)
            
            self.total_detections += len(detections)
            return detections
            
        except Exception as e:
            logger.error(f"Error in license plate detection: {str(e)}")
            return []
    
    def preprocess_plate_image(self, plate_img: np.ndarray) -> np.ndarray:
        """
        Preprocess license plate image for better OCR
        
        Args:
            plate_img: Cropped license plate image
            
        Returns:
            Preprocessed image
        """
        # Convert to grayscale
        if len(plate_img.shape) == 3:
            gray = cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY)
        else:
            gray = plate_img
        
        # Resize for better OCR (minimum height of 64 pixels)
        height, width = gray.shape
        if height < 64:
            scale = 64 / height
            new_width = int(width * scale)
            gray = cv2.resize(gray, (new_width, 64), interpolation=cv2.INTER_CUBIC)
        
        # Apply bilateral filter to reduce noise while keeping edges sharp
        filtered = cv2.bilateralFilter(gray, 11, 17, 17)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            filtered, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def recognize_text_easyocr(self, plate_img: np.ndarray) -> str:
        """
        Recognize text using EasyOCR
        
        Args:
            plate_img: Preprocessed license plate image
            
        Returns:
            Recognized text string
        """
        try:
            results = self.ocr_reader.readtext(plate_img, detail=0, paragraph=False)
            
            if results:
                # Join all detected text
                text = ' '.join(results)
                return self.clean_license_plate_text(text)
            
            return ""
            
        except Exception as e:
            logger.error(f"EasyOCR recognition error: {str(e)}")
            return ""
    
    def recognize_text_tesseract(self, plate_img: np.ndarray) -> str:
        """
        Recognize text using Tesseract
        
        Args:
            plate_img: Preprocessed license plate image
            
        Returns:
            Recognized text string
        """
        try:
            # Configure tesseract for license plates
            config = '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
            
            text = pytesseract.image_to_string(plate_img, config=config)
            return self.clean_license_plate_text(text)
            
        except Exception as e:
            logger.error(f"Tesseract recognition error: {str(e)}")
            return ""
    
    def clean_license_plate_text(self, text: str) -> str:
        """
        Clean and format license plate text
        
        Args:
            text: Raw OCR text
            
        Returns:
            Cleaned license plate text
        """
        # Remove non-alphanumeric characters
        cleaned = re.sub(r'[^A-Z0-9]', '', text.upper())
        
        # Remove very short results (likely noise)
        if len(cleaned) < 3:
            return ""
        
        # Basic validation for common license plate patterns
        # This can be customized based on regional patterns
        if len(cleaned) > 10:  # Too long, likely incorrect
            return ""
        
        return cleaned
    
    def recognize_license_plates(self, frame: np.ndarray, detections: List[Dict]) -> List[Dict]:
        """
        Recognize text in detected license plates
        
        Args:
            frame: Original frame
            detections: List of license plate detections
            
        Returns:
            List of detections with recognized text
        """
        results = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            
            # Extract license plate region with some padding
            padding = 5
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(frame.shape[1], x2 + padding)
            y2 = min(frame.shape[0], y2 + padding)
            
            plate_img = frame[y1:y2, x1:x2]
            
            if plate_img.size == 0:
                continue
            
            # Preprocess image
            processed_img = self.preprocess_plate_image(plate_img)
            
            # Recognize text
            if self.ocr_engine == "easyocr":
                text = self.recognize_text_easyocr(processed_img)
            else:
                text = self.recognize_text_tesseract(processed_img)
            
            # Add text to detection
            detection['text'] = text
            detection['processed_image'] = processed_img
            
            if text:
                self.successful_reads += 1
                logger.debug(f"Recognized license plate: {text}")
            
            results.append(detection)
        
        return results
    
    def draw_license_plates(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw license plate detections and recognized text
        
        Args:
            frame: Input frame
            detections: List of license plate detections with text
            
        Returns:
            Frame with drawn detections
        """
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            text = detection.get('text', '')
            
            # Draw bounding box
            color = (0, 255, 255)  # Yellow for license plates
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            
            # Prepare label
            label_parts = [f"LP: {confidence:.2f}"]
            if text:
                label_parts.append(f"Text: {text}")
            
            label = " | ".join(label_parts)
            
            # Draw label background and text
            (label_width, label_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
            )
            
            cv2.rectangle(
                frame,
                (x1, y1 - label_height - 10),
                (x1 + label_width, y1),
                color,
                -1
            )
            
            cv2.putText(
                frame,
                label,
                (x1, y1 - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (0, 0, 0),
                2
            )
        
        return frame
    
    def get_statistics(self) -> Dict:
        """Get recognition statistics"""
        success_rate = (self.successful_reads / max(self.total_detections, 1)) * 100
        
        return {
            'total_detections': self.total_detections,
            'successful_reads': self.successful_reads,
            'success_rate': success_rate,
            'ocr_engine': self.ocr_engine,
            'confidence_threshold': self.confidence_threshold
        }


# Example usage
if __name__ == "__main__":
    detector = LicensePlateDetector()
    
    # Test with webcam
    cap = cv2.VideoCapture(0)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Detect license plates
        detections = detector.detect_license_plates(frame)
        
        # Recognize text
        detections_with_text = detector.recognize_license_plates(frame, detections)
        
        # Draw results
        frame_with_plates = detector.draw_license_plates(frame, detections_with_text)
        
        cv2.imshow('License Plate Detection', frame_with_plates)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
