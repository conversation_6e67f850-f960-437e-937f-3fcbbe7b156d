"""
Traffic Sign Detection and Classification Module
Detects and classifies traffic signs for traffic rule enforcement
"""

import cv2
import numpy as np
import torch
from ultralytics import YOL<PERSON>
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class TrafficSignDetector:
    """
    Traffic sign detection and classification system
    Detects various traffic signs including speed limits, stop signs, traffic lights
    """
    
    # Common traffic sign classes (can be extended based on dataset)
    TRAFFIC_SIGN_CLASSES = {
        0: 'speed_limit_20',
        1: 'speed_limit_30', 
        2: 'speed_limit_50',
        3: 'speed_limit_60',
        4: 'speed_limit_70',
        5: 'speed_limit_80',
        6: 'end_speed_limit_80',
        7: 'speed_limit_100',
        8: 'speed_limit_120',
        9: 'no_passing',
        10: 'no_passing_vehicles_over_3.5_tons',
        11: 'right_of_way_at_intersection',
        12: 'priority_road',
        13: 'yield',
        14: 'stop',
        15: 'no_vehicles',
        16: 'vehicles_over_3.5_tons_prohibited',
        17: 'no_entry',
        18: 'general_caution',
        19: 'dangerous_curve_left',
        20: 'dangerous_curve_right',
        21: 'double_curve',
        22: 'bumpy_road',
        23: 'slippery_road',
        24: 'road_narrows_on_right',
        25: 'road_work',
        26: 'traffic_signals',
        27: 'pedestrians',
        28: 'children_crossing',
        29: 'bicycles_crossing',
        30: 'beware_of_ice_snow',
        31: 'wild_animals_crossing',
        32: 'end_speed_and_passing_limits',
        33: 'turn_right_ahead',
        34: 'turn_left_ahead',
        35: 'ahead_only',
        36: 'go_straight_or_right',
        37: 'go_straight_or_left',
        38: 'keep_right',
        39: 'keep_left',
        40: 'roundabout_mandatory',
        41: 'end_of_no_passing',
        42: 'end_no_passing_vehicles_over_3.5_tons'
    }
    
    # Traffic light states (if using COCO classes)
    TRAFFIC_LIGHT_STATES = {
        'red': (0, 0, 255),
        'yellow': (0, 255, 255),
        'green': (0, 255, 0)
    }
    
    def __init__(self, model_path: str = "models/traffic_sign_classifier.pt",
                 device: str = "cuda", confidence_threshold: float = 0.8):
        """
        Initialize traffic sign detector
        
        Args:
            model_path: Path to traffic sign classification model
            device: Device for inference ('cuda' or 'cpu')
            confidence_threshold: Minimum confidence for detections
        """
        self.model_path = model_path
        self.device = device
        self.confidence_threshold = confidence_threshold
        
        # Initialize model
        self.model = None
        self.load_model()
        
        # Statistics
        self.total_detections = 0
        self.sign_counts = {}
        
    def load_model(self):
        """Load the traffic sign detection model"""
        try:
            logger.info(f"Loading traffic sign model from {self.model_path}")
            
            # Try to load custom model, fallback to YOLOv8 if not available
            try:
                self.model = YOLO(self.model_path)
            except:
                logger.warning("Custom traffic sign model not found, using YOLOv8n")
                self.model = YOLO("yolov8n.pt")
            
            # Move to device
            if self.device == "cuda" and torch.cuda.is_available():
                self.model.to("cuda")
                logger.info("Traffic sign model loaded on GPU")
            else:
                self.model.to("cpu")
                logger.info("Traffic sign model loaded on CPU")
                
        except Exception as e:
            logger.error(f"Failed to load traffic sign model: {str(e)}")
            raise
    
    def detect_traffic_signs(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect traffic signs in frame
        
        Args:
            frame: Input image frame
            
        Returns:
            List of traffic sign detections
        """
        if self.model is None:
            return []
        
        try:
            # Run detection
            results = self.model(
                frame,
                conf=self.confidence_threshold,
                verbose=False
            )
            
            detections = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Get class name
                        class_name = self.TRAFFIC_SIGN_CLASSES.get(
                            class_id, f"unknown_{class_id}"
                        )
                        
                        detection = {
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'class_name': class_name,
                            'center': (int((x1 + x2) / 2), int((y1 + y2) / 2)),
                            'area': (x2 - x1) * (y2 - y1)
                        }
                        
                        detections.append(detection)
                        
                        # Update statistics
                        if class_name not in self.sign_counts:
                            self.sign_counts[class_name] = 0
                        self.sign_counts[class_name] += 1
            
            self.total_detections += len(detections)
            return detections
            
        except Exception as e:
            logger.error(f"Error in traffic sign detection: {str(e)}")
            return []
    
    def detect_traffic_lights(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect traffic lights and determine their state
        
        Args:
            frame: Input image frame
            
        Returns:
            List of traffic light detections with states
        """
        # First detect traffic lights using YOLO (class 9 in COCO)
        results = self.model(frame, classes=[9], conf=self.confidence_threshold, verbose=False)
        
        traffic_lights = []
        
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    
                    # Extract traffic light region
                    tl_region = frame[int(y1):int(y2), int(x1):int(x2)]
                    
                    # Determine traffic light state
                    state = self.classify_traffic_light_state(tl_region)
                    
                    detection = {
                        'bbox': [int(x1), int(y1), int(x2), int(y2)],
                        'confidence': float(confidence),
                        'class_name': 'traffic_light',
                        'state': state,
                        'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                    }
                    
                    traffic_lights.append(detection)
        
        return traffic_lights
    
    def classify_traffic_light_state(self, tl_region: np.ndarray) -> str:
        """
        Classify traffic light state based on color analysis
        
        Args:
            tl_region: Cropped traffic light image
            
        Returns:
            Traffic light state ('red', 'yellow', 'green', 'unknown')
        """
        if tl_region.size == 0:
            return 'unknown'
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(tl_region, cv2.COLOR_BGR2HSV)
        
        # Define color ranges in HSV
        red_lower1 = np.array([0, 50, 50])
        red_upper1 = np.array([10, 255, 255])
        red_lower2 = np.array([170, 50, 50])
        red_upper2 = np.array([180, 255, 255])
        
        yellow_lower = np.array([20, 50, 50])
        yellow_upper = np.array([30, 255, 255])
        
        green_lower = np.array([40, 50, 50])
        green_upper = np.array([80, 255, 255])
        
        # Create masks
        red_mask1 = cv2.inRange(hsv, red_lower1, red_upper1)
        red_mask2 = cv2.inRange(hsv, red_lower2, red_upper2)
        red_mask = red_mask1 + red_mask2
        
        yellow_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)
        green_mask = cv2.inRange(hsv, green_lower, green_upper)
        
        # Count pixels for each color
        red_pixels = cv2.countNonZero(red_mask)
        yellow_pixels = cv2.countNonZero(yellow_mask)
        green_pixels = cv2.countNonZero(green_mask)
        
        # Determine dominant color
        max_pixels = max(red_pixels, yellow_pixels, green_pixels)
        
        if max_pixels < 10:  # Too few colored pixels
            return 'unknown'
        
        if red_pixels == max_pixels:
            return 'red'
        elif yellow_pixels == max_pixels:
            return 'yellow'
        elif green_pixels == max_pixels:
            return 'green'
        else:
            return 'unknown'
    
    def extract_speed_limit(self, sign_name: str) -> Optional[int]:
        """
        Extract speed limit value from sign name
        
        Args:
            sign_name: Traffic sign class name
            
        Returns:
            Speed limit value in km/h or None
        """
        if 'speed_limit_' in sign_name:
            try:
                # Extract number from sign name
                speed_str = sign_name.replace('speed_limit_', '')
                return int(speed_str)
            except ValueError:
                return None
        return None
    
    def draw_traffic_signs(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw traffic sign detections on frame
        
        Args:
            frame: Input frame
            detections: List of traffic sign detections
            
        Returns:
            Frame with drawn detections
        """
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            class_name = detection['class_name']
            confidence = detection['confidence']
            
            # Choose color based on sign type
            if 'speed_limit' in class_name:
                color = (255, 0, 0)  # Blue for speed limits
            elif class_name == 'stop':
                color = (0, 0, 255)  # Red for stop signs
            elif class_name == 'yield':
                color = (0, 255, 255)  # Yellow for yield
            elif 'traffic_light' in class_name:
                state = detection.get('state', 'unknown')
                color = self.TRAFFIC_LIGHT_STATES.get(state, (128, 128, 128))
            else:
                color = (0, 255, 0)  # Green for other signs
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            
            # Prepare label
            label = f"{class_name.replace('_', ' ').title()}: {confidence:.2f}"
            
            # Add traffic light state if applicable
            if 'state' in detection:
                label += f" ({detection['state'].upper()})"
            
            # Add speed limit if applicable
            speed_limit = self.extract_speed_limit(class_name)
            if speed_limit:
                label += f" ({speed_limit} km/h)"
            
            # Draw label
            (label_width, label_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
            )
            
            cv2.rectangle(
                frame,
                (x1, y1 - label_height - 10),
                (x1 + label_width, y1),
                color,
                -1
            )
            
            cv2.putText(
                frame,
                label,
                (x1, y1 - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                2
            )
        
        return frame
    
    def get_statistics(self) -> Dict:
        """Get detection statistics"""
        return {
            'total_detections': self.total_detections,
            'sign_counts': self.sign_counts.copy(),
            'confidence_threshold': self.confidence_threshold,
            'unique_signs_detected': len(self.sign_counts)
        }
    
    def reset_statistics(self):
        """Reset detection statistics"""
        self.total_detections = 0
        self.sign_counts.clear()


# Example usage
if __name__ == "__main__":
    detector = TrafficSignDetector()
    
    # Test with webcam
    cap = cv2.VideoCapture(0)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Detect traffic signs
        sign_detections = detector.detect_traffic_signs(frame)
        
        # Detect traffic lights
        light_detections = detector.detect_traffic_lights(frame)
        
        # Combine detections
        all_detections = sign_detections + light_detections
        
        # Draw results
        frame_with_signs = detector.draw_traffic_signs(frame, all_detections)
        
        cv2.imshow('Traffic Sign Detection', frame_with_signs)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
