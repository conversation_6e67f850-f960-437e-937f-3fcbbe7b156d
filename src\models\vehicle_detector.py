"""
Vehicle Detection Module using YOLOv8
Handles real-time vehicle detection and classification
"""

import cv2
import numpy as np
import torch
from ultralytics import YOLO
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class VehicleDetector:
    """
    Vehicle detection using YOLOv8 model
    Supports detection of cars, trucks, buses, motorcycles, and other vehicles
    """
    
    # COCO dataset class IDs for vehicles
    VEHICLE_CLASSES = {
        2: 'car',
        3: 'motorcycle', 
        5: 'bus',
        7: 'truck',
        1: 'bicycle'  # Optional: include bicycles
    }
    
    def __init__(self, model_path: str = "yolov8n.pt", device: str = "cuda", 
                 confidence_threshold: float = 0.5, iou_threshold: float = 0.45):
        """
        Initialize the vehicle detector
        
        Args:
            model_path: Path to YOLOv8 model file
            device: Device to run inference on ('cuda' or 'cpu')
            confidence_threshold: Minimum confidence for detections
            iou_threshold: IoU threshold for NMS
        """
        self.model_path = model_path
        self.device = device
        self.confidence_threshold = confidence_threshold
        self.iou_threshold = iou_threshold
        
        # Initialize model
        self.model = None
        self.load_model()
        
        # Detection statistics
        self.total_detections = 0
        self.frame_count = 0
        
    def load_model(self):
        """Load the YOLOv8 model"""
        try:
            logger.info(f"Loading YOLOv8 model from {self.model_path}")
            self.model = YOLO(self.model_path)
            
            # Move model to specified device
            if self.device == "cuda" and torch.cuda.is_available():
                self.model.to("cuda")
                logger.info("Model loaded on GPU")
            else:
                self.model.to("cpu")
                logger.info("Model loaded on CPU")
                
        except Exception as e:
            logger.error(f"Failed to load model: {str(e)}")
            raise
    
    def detect_vehicles(self, frame: np.ndarray) -> List[Dict]:
        """
        Detect vehicles in a frame
        
        Args:
            frame: Input image frame
            
        Returns:
            List of detection dictionaries containing:
            - bbox: [x1, y1, x2, y2] bounding box coordinates
            - confidence: Detection confidence score
            - class_id: Vehicle class ID
            - class_name: Vehicle class name
            - center: (x, y) center point of bounding box
        """
        if self.model is None:
            logger.error("Model not loaded")
            return []
        
        try:
            # Run inference
            results = self.model(
                frame,
                conf=self.confidence_threshold,
                iou=self.iou_threshold,
                verbose=False
            )
            
            detections = []
            
            # Process results
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get box coordinates and confidence
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Filter for vehicle classes only
                        if class_id in self.VEHICLE_CLASSES:
                            # Calculate center point
                            center_x = int((x1 + x2) / 2)
                            center_y = int((y1 + y2) / 2)
                            
                            detection = {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': float(confidence),
                                'class_id': class_id,
                                'class_name': self.VEHICLE_CLASSES[class_id],
                                'center': (center_x, center_y),
                                'area': (x2 - x1) * (y2 - y1)
                            }
                            
                            detections.append(detection)
            
            self.total_detections += len(detections)
            self.frame_count += 1
            
            return detections
            
        except Exception as e:
            logger.error(f"Error during vehicle detection: {str(e)}")
            return []
    
    def draw_detections(self, frame: np.ndarray, detections: List[Dict], 
                       draw_labels: bool = True, draw_confidence: bool = True) -> np.ndarray:
        """
        Draw detection bounding boxes and labels on frame
        
        Args:
            frame: Input frame
            detections: List of detection dictionaries
            draw_labels: Whether to draw class labels
            draw_confidence: Whether to draw confidence scores
            
        Returns:
            Frame with drawn detections
        """
        # Define colors for different vehicle types
        colors = {
            'car': (0, 255, 0),        # Green
            'truck': (0, 0, 255),      # Red
            'bus': (255, 0, 0),        # Blue
            'motorcycle': (255, 255, 0), # Cyan
            'bicycle': (255, 0, 255)   # Magenta
        }
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            class_name = detection['class_name']
            confidence = detection['confidence']
            
            # Get color for vehicle type
            color = colors.get(class_name, (128, 128, 128))  # Default gray
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            
            # Draw label and confidence
            if draw_labels or draw_confidence:
                label_parts = []
                if draw_labels:
                    label_parts.append(class_name.capitalize())
                if draw_confidence:
                    label_parts.append(f"{confidence:.2f}")
                
                label = " ".join(label_parts)
                
                # Calculate label size and position
                (label_width, label_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
                )
                
                # Draw label background
                cv2.rectangle(
                    frame, 
                    (x1, y1 - label_height - 10),
                    (x1 + label_width, y1),
                    color, 
                    -1
                )
                
                # Draw label text
                cv2.putText(
                    frame, 
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (255, 255, 255),
                    2
                )
        
        return frame
    
    def get_statistics(self) -> Dict:
        """Get detection statistics"""
        avg_detections = self.total_detections / max(self.frame_count, 1)
        
        return {
            'total_detections': self.total_detections,
            'frames_processed': self.frame_count,
            'average_detections_per_frame': avg_detections,
            'model_path': self.model_path,
            'device': self.device,
            'confidence_threshold': self.confidence_threshold
        }
    
    def reset_statistics(self):
        """Reset detection statistics"""
        self.total_detections = 0
        self.frame_count = 0
    
    def update_thresholds(self, confidence: Optional[float] = None, 
                         iou: Optional[float] = None):
        """Update detection thresholds"""
        if confidence is not None:
            self.confidence_threshold = confidence
            logger.info(f"Updated confidence threshold to {confidence}")
        
        if iou is not None:
            self.iou_threshold = iou
            logger.info(f"Updated IoU threshold to {iou}")


# Example usage and testing
if __name__ == "__main__":
    # Test the vehicle detector
    detector = VehicleDetector()
    
    # Test with webcam
    cap = cv2.VideoCapture(0)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Detect vehicles
        detections = detector.detect_vehicles(frame)
        
        # Draw detections
        frame_with_detections = detector.draw_detections(frame, detections)
        
        # Display frame
        cv2.imshow('Vehicle Detection', frame_with_detections)
        
        # Print statistics every 100 frames
        if detector.frame_count % 100 == 0:
            stats = detector.get_statistics()
            print(f"Processed {stats['frames_processed']} frames, "
                  f"Average detections: {stats['average_detections_per_frame']:.2f}")
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
