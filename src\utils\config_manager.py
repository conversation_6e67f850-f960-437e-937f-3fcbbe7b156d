"""
Configuration Manager for Traffic Surveillance System
Handles loading and validation of configuration files
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    Configuration manager for loading and validating system configuration
    """
    
    def __init__(self, config_path: str):
        """
        Initialize configuration manager
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = Path(config_path)
        self.config = {}
        
        # Load configuration
        self.load_config()
        
        # Validate configuration
        self.validate_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                if self.config_path.suffix.lower() in ['.yaml', '.yml']:
                    self.config = yaml.safe_load(file)
                elif self.config_path.suffix.lower() == '.json':
                    self.config = json.load(file)
                else:
                    raise ValueError(f"Unsupported configuration file format: {self.config_path.suffix}")
            
            logger.info(f"Configuration loaded from: {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {str(e)}")
            raise
    
    def validate_config(self):
        """Validate configuration structure and values"""
        try:
            # Check required sections
            required_sections = ['video', 'models', 'analytics', 'database']
            
            for section in required_sections:
                if section not in self.config:
                    raise ValueError(f"Missing required configuration section: {section}")
            
            # Validate video configuration
            self._validate_video_config()
            
            # Validate models configuration
            self._validate_models_config()
            
            # Validate analytics configuration
            self._validate_analytics_config()
            
            # Validate database configuration
            self._validate_database_config()
            
            logger.info("Configuration validation successful")
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {str(e)}")
            raise
    
    def _validate_video_config(self):
        """Validate video configuration"""
        video_config = self.config['video']
        
        # Check video sources
        if 'sources' not in video_config or not video_config['sources']:
            raise ValueError("No video sources specified in configuration")
        
        # Set default values
        video_config.setdefault('fps', 30)
        video_config.setdefault('resolution', {'width': 1920, 'height': 1080})
        video_config.setdefault('skip_frames', 1)
        video_config.setdefault('buffer_size', 10)
        
        # Validate resolution
        resolution = video_config['resolution']
        if not isinstance(resolution, dict) or 'width' not in resolution or 'height' not in resolution:
            raise ValueError("Invalid resolution configuration")
        
        if resolution['width'] <= 0 or resolution['height'] <= 0:
            raise ValueError("Resolution width and height must be positive")
    
    def _validate_models_config(self):
        """Validate models configuration"""
        models_config = self.config['models']
        
        # Vehicle detection is required
        if 'vehicle_detection' not in models_config:
            raise ValueError("Vehicle detection model configuration is required")
        
        vehicle_config = models_config['vehicle_detection']
        
        # Set default values for vehicle detection
        vehicle_config.setdefault('model_path', 'yolov8n.pt')
        vehicle_config.setdefault('confidence_threshold', 0.5)
        vehicle_config.setdefault('iou_threshold', 0.45)
        vehicle_config.setdefault('device', 'cuda')
        
        # Validate thresholds
        if not (0 < vehicle_config['confidence_threshold'] <= 1):
            raise ValueError("Confidence threshold must be between 0 and 1")
        
        if not (0 < vehicle_config['iou_threshold'] <= 1):
            raise ValueError("IoU threshold must be between 0 and 1")
        
        # Validate optional models
        for model_name in ['license_plate', 'traffic_signs']:
            if model_name in models_config:
                model_config = models_config[model_name]
                model_config.setdefault('confidence_threshold', 0.7)
                
                if not (0 < model_config['confidence_threshold'] <= 1):
                    raise ValueError(f"{model_name} confidence threshold must be between 0 and 1")
    
    def _validate_analytics_config(self):
        """Validate analytics configuration"""
        analytics_config = self.config['analytics']
        
        # Set default values
        analytics_config.setdefault('speed_estimation', {'enabled': False})
        analytics_config.setdefault('counting', {'enabled': False})
        analytics_config.setdefault('flow_analysis', {'enabled': False})
        analytics_config.setdefault('violations', {})
        
        # Validate speed estimation
        speed_config = analytics_config['speed_estimation']
        if speed_config.get('enabled', False):
            speed_config.setdefault('calibration_distance', 10)
            speed_config.setdefault('calibration_pixels', 100)
            
            if speed_config['calibration_distance'] <= 0:
                raise ValueError("Calibration distance must be positive")
            
            if speed_config['calibration_pixels'] <= 0:
                raise ValueError("Calibration pixels must be positive")
        
        # Validate counting configuration
        counting_config = analytics_config['counting']
        if counting_config.get('enabled', False):
            if 'counting_lines' in counting_config:
                for line in counting_config['counting_lines']:
                    if 'name' not in line or 'coordinates' not in line:
                        raise ValueError("Counting line must have name and coordinates")
                    
                    if len(line['coordinates']) != 2:
                        raise ValueError("Counting line must have exactly 2 coordinate points")
        
        # Validate violations configuration
        violations_config = analytics_config['violations']
        violations_config.setdefault('speed_limit', 60)
        violations_config.setdefault('red_light_violation', False)
        violations_config.setdefault('wrong_way_detection', False)
        violations_config.setdefault('parking_violation', False)
        
        if violations_config['speed_limit'] <= 0:
            raise ValueError("Speed limit must be positive")
    
    def _validate_database_config(self):
        """Validate database configuration"""
        db_config = self.config['database']
        
        # Set default values
        db_config.setdefault('type', 'sqlite')
        
        if db_config['type'] == 'sqlite':
            db_config.setdefault('path', 'data/traffic_surveillance.db')
        elif db_config['type'] in ['postgresql', 'mysql']:
            required_fields = ['host', 'port', 'username', 'password', 'database']
            for field in required_fields:
                if field not in db_config:
                    raise ValueError(f"Missing required database field: {field}")
        else:
            raise ValueError(f"Unsupported database type: {db_config['type']}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get the complete configuration"""
        return self.config.copy()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get a specific configuration section
        
        Args:
            section: Section name
            
        Returns:
            Configuration section
        """
        return self.config.get(section, {}).copy()
    
    def update_config(self, updates: Dict[str, Any]):
        """
        Update configuration with new values
        
        Args:
            updates: Dictionary of updates to apply
        """
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, updates)
        
        # Re-validate after updates
        self.validate_config()
        
        logger.info("Configuration updated successfully")
    
    def save_config(self, output_path: Optional[str] = None):
        """
        Save configuration to file
        
        Args:
            output_path: Output file path (defaults to original path)
        """
        try:
            save_path = Path(output_path) if output_path else self.config_path
            
            with open(save_path, 'w', encoding='utf-8') as file:
                if save_path.suffix.lower() in ['.yaml', '.yml']:
                    yaml.dump(self.config, file, default_flow_style=False, indent=2)
                elif save_path.suffix.lower() == '.json':
                    json.dump(self.config, file, indent=2)
                else:
                    raise ValueError(f"Unsupported output format: {save_path.suffix}")
            
            logger.info(f"Configuration saved to: {save_path}")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {str(e)}")
            raise
    
    def get_model_path(self, model_name: str) -> str:
        """
        Get model path with fallback to default
        
        Args:
            model_name: Name of the model
            
        Returns:
            Model file path
        """
        models_config = self.config.get('models', {})
        model_config = models_config.get(model_name, {})
        
        return model_config.get('model_path', f'models/{model_name}.pt')
    
    def is_feature_enabled(self, feature_path: str) -> bool:
        """
        Check if a feature is enabled
        
        Args:
            feature_path: Dot-separated path to feature (e.g., 'analytics.speed_estimation.enabled')
            
        Returns:
            True if feature is enabled, False otherwise
        """
        try:
            parts = feature_path.split('.')
            current = self.config
            
            for part in parts:
                current = current[part]
            
            return bool(current)
            
        except (KeyError, TypeError):
            return False
    
    def get_nested_value(self, path: str, default: Any = None) -> Any:
        """
        Get nested configuration value
        
        Args:
            path: Dot-separated path to value
            default: Default value if path not found
            
        Returns:
            Configuration value or default
        """
        try:
            parts = path.split('.')
            current = self.config
            
            for part in parts:
                current = current[part]
            
            return current
            
        except (KeyError, TypeError):
            return default


# Example usage
if __name__ == "__main__":
    # Test configuration manager
    try:
        config_manager = ConfigManager('config.yaml')
        
        # Get full configuration
        config = config_manager.get_config()
        print("Configuration loaded successfully")
        
        # Get specific section
        video_config = config_manager.get_section('video')
        print(f"Video sources: {video_config.get('sources', [])}")
        
        # Check if feature is enabled
        speed_enabled = config_manager.is_feature_enabled('analytics.speed_estimation.enabled')
        print(f"Speed estimation enabled: {speed_enabled}")
        
        # Get nested value
        speed_limit = config_manager.get_nested_value('analytics.violations.speed_limit', 60)
        print(f"Speed limit: {speed_limit}")
        
    except Exception as e:
        print(f"Configuration error: {str(e)}")
