"""
Database Manager for Traffic Surveillance System
Handles data storage and retrieval for detections, analytics, and violations
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Database manager for traffic surveillance data
    Supports SQLite with extensibility for other databases
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize database manager
        
        Args:
            config: Database configuration
        """
        self.config = config
        self.db_type = config.get('type', 'sqlite')
        self.db_path = config.get('path', 'data/traffic_surveillance.db')
        
        self.connection = None
        self.cursor = None
        
        # Initialize database
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection and create tables"""
        try:
            if self.db_type == 'sqlite':
                # Create directory if it doesn't exist
                Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
                
                self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
                self.cursor = self.connection.cursor()
                
                # Enable foreign keys
                self.cursor.execute("PRAGMA foreign_keys = ON")
                
                logger.info(f"Connected to SQLite database: {self.db_path}")
            
            else:
                raise ValueError(f"Unsupported database type: {self.db_type}")
            
            # Create tables
            self._create_tables()
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise
    
    def _create_tables(self):
        """Create database tables"""
        try:
            # Vehicle detections table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS vehicle_detections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    vehicle_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    bbox_x1 INTEGER NOT NULL,
                    bbox_y1 INTEGER NOT NULL,
                    bbox_x2 INTEGER NOT NULL,
                    bbox_y2 INTEGER NOT NULL,
                    center_x INTEGER NOT NULL,
                    center_y INTEGER NOT NULL,
                    area REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # License plate detections table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS license_plates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    plate_text TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    bbox_x1 INTEGER NOT NULL,
                    bbox_y1 INTEGER NOT NULL,
                    bbox_x2 INTEGER NOT NULL,
                    bbox_y2 INTEGER NOT NULL,
                    vehicle_detection_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (vehicle_detection_id) REFERENCES vehicle_detections (id)
                )
            """)
            
            # Traffic violations table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS traffic_violations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    violation_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    vehicle_id INTEGER,
                    speed REAL,
                    speed_limit REAL,
                    position_x INTEGER,
                    position_y INTEGER,
                    license_plate TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Traffic analytics table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS traffic_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    vehicle_count INTEGER NOT NULL,
                    average_speed REAL,
                    traffic_flow TEXT,
                    congestion_level REAL,
                    entry_count INTEGER DEFAULT 0,
                    exit_count INTEGER DEFAULT 0,
                    violations_count INTEGER DEFAULT 0,
                    analytics_data TEXT,  -- JSON data
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Traffic signs detections table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS traffic_signs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    sign_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    bbox_x1 INTEGER NOT NULL,
                    bbox_y1 INTEGER NOT NULL,
                    bbox_x2 INTEGER NOT NULL,
                    bbox_y2 INTEGER NOT NULL,
                    speed_limit INTEGER,
                    state TEXT,  -- For traffic lights
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # System statistics table
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp REAL NOT NULL,
                    processing_fps REAL,
                    frames_processed INTEGER,
                    frames_dropped INTEGER,
                    uptime REAL,
                    memory_usage REAL,
                    cpu_usage REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better performance
            self._create_indexes()
            
            self.connection.commit()
            logger.info("Database tables created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
            raise
    
    def _create_indexes(self):
        """Create database indexes for better query performance"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_vehicle_timestamp ON vehicle_detections(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_license_timestamp ON license_plates(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_violations_timestamp ON traffic_violations(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_violations_type ON traffic_violations(violation_type)",
            "CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON traffic_analytics(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_signs_timestamp ON traffic_signs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_stats_timestamp ON system_stats(timestamp)"
        ]
        
        for index_sql in indexes:
            self.cursor.execute(index_sql)
    
    def save_vehicle_detection(self, detection: Dict[str, Any], timestamp: float) -> int:
        """
        Save vehicle detection to database
        
        Args:
            detection: Vehicle detection data
            timestamp: Detection timestamp
            
        Returns:
            Detection ID
        """
        try:
            bbox = detection['bbox']
            center = detection['center']
            
            self.cursor.execute("""
                INSERT INTO vehicle_detections 
                (timestamp, vehicle_type, confidence, bbox_x1, bbox_y1, bbox_x2, bbox_y2, 
                 center_x, center_y, area)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp,
                detection['class_name'],
                detection['confidence'],
                bbox[0], bbox[1], bbox[2], bbox[3],
                center[0], center[1],
                detection.get('area', 0)
            ))
            
            detection_id = self.cursor.lastrowid
            self.connection.commit()
            
            return detection_id
            
        except Exception as e:
            logger.error(f"Failed to save vehicle detection: {str(e)}")
            return -1
    
    def save_license_plate(self, plate_data: Dict[str, Any], timestamp: float, 
                          vehicle_detection_id: Optional[int] = None) -> int:
        """
        Save license plate detection to database
        
        Args:
            plate_data: License plate data
            timestamp: Detection timestamp
            vehicle_detection_id: Associated vehicle detection ID
            
        Returns:
            License plate ID
        """
        try:
            bbox = plate_data['bbox']
            
            self.cursor.execute("""
                INSERT INTO license_plates 
                (timestamp, plate_text, confidence, bbox_x1, bbox_y1, bbox_x2, bbox_y2, 
                 vehicle_detection_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp,
                plate_data.get('text', ''),
                plate_data['confidence'],
                bbox[0], bbox[1], bbox[2], bbox[3],
                vehicle_detection_id
            ))
            
            plate_id = self.cursor.lastrowid
            self.connection.commit()
            
            return plate_id
            
        except Exception as e:
            logger.error(f"Failed to save license plate: {str(e)}")
            return -1
    
    def save_violation(self, violation: Dict[str, Any], timestamp: float) -> int:
        """
        Save traffic violation to database
        
        Args:
            violation: Violation data
            timestamp: Violation timestamp
            
        Returns:
            Violation ID
        """
        try:
            position = violation.get('position', (0, 0))
            
            self.cursor.execute("""
                INSERT INTO traffic_violations 
                (timestamp, violation_type, severity, vehicle_id, speed, speed_limit,
                 position_x, position_y, license_plate, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp,
                violation['type'],
                violation.get('severity', 'medium'),
                violation.get('vehicle_id'),
                violation.get('speed'),
                violation.get('speed_limit'),
                position[0], position[1],
                violation.get('license_plate'),
                violation.get('description', '')
            ))
            
            violation_id = self.cursor.lastrowid
            self.connection.commit()
            
            return violation_id
            
        except Exception as e:
            logger.error(f"Failed to save violation: {str(e)}")
            return -1
    
    def save_analytics(self, analytics: Dict[str, Any], timestamp: float) -> int:
        """
        Save traffic analytics data to database
        
        Args:
            analytics: Analytics data
            timestamp: Analytics timestamp
            
        Returns:
            Analytics ID
        """
        try:
            counting_data = analytics.get('counting', {})
            
            self.cursor.execute("""
                INSERT INTO traffic_analytics 
                (timestamp, vehicle_count, average_speed, traffic_flow, congestion_level,
                 entry_count, exit_count, violations_count, analytics_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                timestamp,
                analytics.get('vehicle_count', 0),
                analytics.get('average_speed', 0),
                analytics.get('traffic_flow', {}).get('status', 'Unknown'),
                analytics.get('traffic_flow', {}).get('congestion_level', 0),
                counting_data.get('total_entry', 0),
                counting_data.get('total_exit', 0),
                analytics.get('violations_count', 0),
                json.dumps(analytics)
            ))
            
            analytics_id = self.cursor.lastrowid
            self.connection.commit()
            
            return analytics_id
            
        except Exception as e:
            logger.error(f"Failed to save analytics: {str(e)}")
            return -1
    
    def get_recent_detections(self, hours: int = 1) -> List[Dict[str, Any]]:
        """
        Get recent vehicle detections
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of detection records
        """
        try:
            cutoff_time = datetime.now().timestamp() - (hours * 3600)
            
            self.cursor.execute("""
                SELECT * FROM vehicle_detections 
                WHERE timestamp > ? 
                ORDER BY timestamp DESC
            """, (cutoff_time,))
            
            columns = [desc[0] for desc in self.cursor.description]
            results = []
            
            for row in self.cursor.fetchall():
                record = dict(zip(columns, row))
                results.append(record)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get recent detections: {str(e)}")
            return []
    
    def get_violations(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get traffic violations
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            List of violation records
        """
        try:
            cutoff_time = datetime.now().timestamp() - (hours * 3600)
            
            self.cursor.execute("""
                SELECT * FROM traffic_violations 
                WHERE timestamp > ? 
                ORDER BY timestamp DESC
            """, (cutoff_time,))
            
            columns = [desc[0] for desc in self.cursor.description]
            results = []
            
            for row in self.cursor.fetchall():
                record = dict(zip(columns, row))
                results.append(record)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get violations: {str(e)}")
            return []
    
    def get_analytics_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get analytics summary
        
        Args:
            hours: Number of hours to look back
            
        Returns:
            Analytics summary
        """
        try:
            cutoff_time = datetime.now().timestamp() - (hours * 3600)
            
            # Get vehicle count statistics
            self.cursor.execute("""
                SELECT COUNT(*) as total_vehicles, AVG(confidence) as avg_confidence
                FROM vehicle_detections 
                WHERE timestamp > ?
            """, (cutoff_time,))
            
            vehicle_stats = dict(zip(['total_vehicles', 'avg_confidence'], 
                                   self.cursor.fetchone()))
            
            # Get violation statistics
            self.cursor.execute("""
                SELECT violation_type, COUNT(*) as count
                FROM traffic_violations 
                WHERE timestamp > ?
                GROUP BY violation_type
            """, (cutoff_time,))
            
            violation_stats = {}
            for row in self.cursor.fetchall():
                violation_stats[row[0]] = row[1]
            
            # Get latest analytics
            self.cursor.execute("""
                SELECT * FROM traffic_analytics 
                WHERE timestamp > ?
                ORDER BY timestamp DESC
                LIMIT 1
            """, (cutoff_time,))
            
            latest_analytics = self.cursor.fetchone()
            
            summary = {
                'vehicle_stats': vehicle_stats,
                'violation_stats': violation_stats,
                'latest_analytics': latest_analytics,
                'time_period_hours': hours
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get analytics summary: {str(e)}")
            return {}
    
    def cleanup_old_data(self, retention_days: int = 30):
        """
        Clean up old data based on retention policy
        
        Args:
            retention_days: Number of days to retain data
        """
        try:
            cutoff_time = datetime.now().timestamp() - (retention_days * 24 * 3600)
            
            tables = [
                'vehicle_detections',
                'license_plates', 
                'traffic_violations',
                'traffic_analytics',
                'traffic_signs',
                'system_stats'
            ]
            
            total_deleted = 0
            
            for table in tables:
                self.cursor.execute(f"""
                    DELETE FROM {table} WHERE timestamp < ?
                """, (cutoff_time,))
                
                deleted_count = self.cursor.rowcount
                total_deleted += deleted_count
                
                logger.info(f"Deleted {deleted_count} old records from {table}")
            
            self.connection.commit()
            logger.info(f"Total {total_deleted} old records deleted")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {str(e)}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            stats = {}
            
            tables = [
                'vehicle_detections',
                'license_plates',
                'traffic_violations', 
                'traffic_analytics',
                'traffic_signs',
                'system_stats'
            ]
            
            for table in tables:
                self.cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = self.cursor.fetchone()[0]
                stats[f"{table}_count"] = count
            
            # Get database file size (SQLite only)
            if self.db_type == 'sqlite':
                db_size = Path(self.db_path).stat().st_size / (1024 * 1024)  # MB
                stats['database_size_mb'] = round(db_size, 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get database stats: {str(e)}")
            return {}
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")


# Example usage
if __name__ == "__main__":
    # Test database manager
    config = {
        'type': 'sqlite',
        'path': 'test_traffic.db'
    }
    
    db_manager = DatabaseManager(config)
    
    # Test saving data
    test_detection = {
        'class_name': 'car',
        'confidence': 0.85,
        'bbox': [100, 100, 200, 200],
        'center': (150, 150),
        'area': 10000
    }
    
    detection_id = db_manager.save_vehicle_detection(test_detection, time.time())
    print(f"Saved detection with ID: {detection_id}")
    
    # Get statistics
    stats = db_manager.get_database_stats()
    print(f"Database stats: {stats}")
    
    db_manager.close()
