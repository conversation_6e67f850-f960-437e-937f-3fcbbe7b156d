"""
Logging Configuration for Traffic Surveillance System
Provides structured logging with file rotation and different log levels
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Dict, Any, Optional
import json
from datetime import datetime


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry)


def setup_logger(config: Dict[str, Any]) -> logging.Logger:
    """
    Setup logging configuration
    
    Args:
        config: Logging configuration dictionary
        
    Returns:
        Configured logger instance
    """
    # Get configuration values
    log_level = config.get('level', 'INFO').upper()
    log_file = config.get('file', 'logs/traffic_surveillance.log')
    max_file_size = config.get('max_file_size', '10MB')
    backup_count = config.get('backup_count', 5)
    console_output = config.get('console_output', True)
    json_format = config.get('json_format', False)
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    if json_format:
        file_formatter = JSONFormatter()
        console_formatter = JSONFormatter()
    else:
        file_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        console_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        file_formatter = logging.Formatter(file_format)
        console_formatter = ColoredFormatter(console_format)
    
    # File handler with rotation
    max_bytes = _parse_size(max_file_size)
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level))
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level))
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # Create application logger
    app_logger = logging.getLogger('traffic_surveillance')
    
    return app_logger


def _parse_size(size_str: str) -> int:
    """
    Parse size string to bytes
    
    Args:
        size_str: Size string (e.g., '10MB', '1GB')
        
    Returns:
        Size in bytes
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        # Assume bytes
        return int(size_str)


class PerformanceLogger:
    """Logger for performance metrics"""
    
    def __init__(self, logger_name: str = 'performance'):
        self.logger = logging.getLogger(logger_name)
        self.start_times = {}
    
    def start_timer(self, operation: str):
        """Start timing an operation"""
        self.start_times[operation] = datetime.now()
    
    def end_timer(self, operation: str, extra_data: Optional[Dict[str, Any]] = None):
        """End timing an operation and log the duration"""
        if operation not in self.start_times:
            self.logger.warning(f"Timer for operation '{operation}' was not started")
            return
        
        duration = (datetime.now() - self.start_times[operation]).total_seconds()
        
        log_data = {
            'operation': operation,
            'duration_seconds': duration,
            'timestamp': datetime.now().isoformat()
        }
        
        if extra_data:
            log_data.update(extra_data)
        
        self.logger.info(f"Operation '{operation}' completed", extra=log_data)
        
        # Clean up
        del self.start_times[operation]
    
    def log_metric(self, metric_name: str, value: float, unit: str = '', 
                   extra_data: Optional[Dict[str, Any]] = None):
        """Log a performance metric"""
        log_data = {
            'metric': metric_name,
            'value': value,
            'unit': unit,
            'timestamp': datetime.now().isoformat()
        }
        
        if extra_data:
            log_data.update(extra_data)
        
        self.logger.info(f"Metric: {metric_name} = {value} {unit}", extra=log_data)


class SystemLogger:
    """Logger for system events and statistics"""
    
    def __init__(self, logger_name: str = 'system'):
        self.logger = logging.getLogger(logger_name)
    
    def log_system_start(self, config: Dict[str, Any]):
        """Log system startup"""
        self.logger.info("Traffic Surveillance System starting", extra={
            'event': 'system_start',
            'config_summary': {
                'video_sources': len(config.get('video', {}).get('sources', [])),
                'models_enabled': list(config.get('models', {}).keys()),
                'analytics_enabled': [
                    k for k, v in config.get('analytics', {}).items() 
                    if isinstance(v, dict) and v.get('enabled', False)
                ]
            }
        })
    
    def log_system_stop(self, uptime_seconds: float, stats: Dict[str, Any]):
        """Log system shutdown"""
        self.logger.info("Traffic Surveillance System stopping", extra={
            'event': 'system_stop',
            'uptime_seconds': uptime_seconds,
            'final_stats': stats
        })
    
    def log_error(self, error: Exception, context: str = ''):
        """Log system errors with context"""
        self.logger.error(f"System error in {context}: {str(error)}", extra={
            'event': 'system_error',
            'error_type': type(error).__name__,
            'context': context
        }, exc_info=True)
    
    def log_detection_stats(self, stats: Dict[str, Any]):
        """Log detection statistics"""
        self.logger.info("Detection statistics", extra={
            'event': 'detection_stats',
            'stats': stats
        })


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


# Context manager for timing operations
class TimedOperation:
    """Context manager for timing operations"""
    
    def __init__(self, operation_name: str, logger: Optional[logging.Logger] = None):
        self.operation_name = operation_name
        self.logger = logger or logging.getLogger('performance')
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            
            if exc_type is None:
                self.logger.info(f"Operation '{self.operation_name}' completed in {duration:.3f}s")
            else:
                self.logger.error(f"Operation '{self.operation_name}' failed after {duration:.3f}s")


# Example usage
if __name__ == "__main__":
    # Test logging setup
    config = {
        'level': 'DEBUG',
        'file': 'test_logs/test.log',
        'max_file_size': '1MB',
        'backup_count': 3,
        'console_output': True,
        'json_format': False
    }
    
    # Setup logger
    logger = setup_logger(config)
    
    # Test different log levels
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test performance logger
    perf_logger = PerformanceLogger()
    perf_logger.start_timer('test_operation')
    
    import time
    time.sleep(0.1)  # Simulate work
    
    perf_logger.end_timer('test_operation', {'frames_processed': 10})
    perf_logger.log_metric('fps', 25.5, 'frames/second')
    
    # Test timed operation context manager
    with TimedOperation('test_context_manager'):
        time.sleep(0.05)
    
    print("Logging test completed")
