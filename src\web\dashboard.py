"""
Web Dashboard for Traffic Surveillance System
Real-time monitoring interface with live video feeds and analytics
"""

from flask import Flask, render_template, jsonify, request, Response
from flask_socketio import Socket<PERSON>, emit
from flask_cors import CORS
import cv2
import json
import base64
import threading
import time
from typing import Dict, Any, Optional
import logging

from ..utils.database_manager import DatabaseManager

logger = logging.getLogger(__name__)


class DashboardManager:
    """
    Manages the web dashboard for traffic surveillance
    """
    
    def __init__(self, config: Dict[str, Any], surveillance_system=None):
        """
        Initialize dashboard manager
        
        Args:
            config: System configuration
            surveillance_system: Reference to surveillance system (optional)
        """
        self.config = config
        self.surveillance_system = surveillance_system
        
        # Initialize database manager
        self.db_manager = DatabaseManager(config['database'])
        
        # Dashboard state
        self.current_frame = None
        self.current_detections = {}
        self.current_analytics = {}
        self.system_stats = {}
        
        # WebSocket clients
        self.connected_clients = set()
        
    def get_current_frame_base64(self) -> Optional[str]:
        """Get current frame as base64 encoded string"""
        if self.surveillance_system and hasattr(self.surveillance_system, 'current_detections'):
            # Get frame from surveillance system
            detections = self.surveillance_system.get_current_detections()
            
            if 'frame' in detections:
                frame = detections['frame']
                
                # Encode frame as JPEG
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                
                # Convert to base64
                frame_base64 = base64.b64encode(buffer).decode('utf-8')
                return f"data:image/jpeg;base64,{frame_base64}"
        
        return None
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get comprehensive dashboard data"""
        # Get current detections and analytics
        if self.surveillance_system:
            current_detections = self.surveillance_system.get_current_detections()
            current_analytics = self.surveillance_system.get_analytics_data()
            system_stats = self.surveillance_system.get_system_statistics()
        else:
            current_detections = {}
            current_analytics = {}
            system_stats = {}
        
        # Get recent data from database
        recent_detections = self.db_manager.get_recent_detections(hours=1)
        recent_violations = self.db_manager.get_violations(hours=24)
        analytics_summary = self.db_manager.get_analytics_summary(hours=24)
        
        return {
            'current': {
                'detections': current_detections,
                'analytics': current_analytics,
                'system_stats': system_stats,
                'frame': self.get_current_frame_base64()
            },
            'recent': {
                'detections': recent_detections[-50:],  # Last 50 detections
                'violations': recent_violations[-20:],   # Last 20 violations
                'analytics_summary': analytics_summary
            },
            'timestamp': time.time()
        }
    
    def get_statistics_summary(self) -> Dict[str, Any]:
        """Get statistics summary for dashboard"""
        # Get database statistics
        db_stats = self.db_manager.get_database_stats()
        
        # Get recent analytics
        analytics_summary = self.db_manager.get_analytics_summary(hours=24)
        
        # Compile summary
        summary = {
            'database': db_stats,
            'analytics': analytics_summary,
            'system': self.system_stats
        }
        
        return summary
    
    def get_violation_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Get violation trends for charts"""
        violations = self.db_manager.get_violations(hours=hours)
        
        # Group violations by type and hour
        violation_trends = {}
        hourly_counts = {}
        
        for violation in violations:
            v_type = violation['violation_type']
            timestamp = violation['timestamp']
            
            # Round to hour
            hour = int(timestamp // 3600) * 3600
            
            if v_type not in violation_trends:
                violation_trends[v_type] = 0
            violation_trends[v_type] += 1
            
            if hour not in hourly_counts:
                hourly_counts[hour] = 0
            hourly_counts[hour] += 1
        
        return {
            'by_type': violation_trends,
            'hourly': hourly_counts,
            'total': len(violations)
        }
    
    def get_traffic_flow_data(self, hours: int = 24) -> Dict[str, Any]:
        """Get traffic flow data for charts"""
        # This would typically come from the analytics table
        # For now, return sample data structure
        return {
            'vehicle_counts': [],
            'average_speeds': [],
            'congestion_levels': [],
            'timestamps': []
        }


def create_app(config: Dict[str, Any], surveillance_system=None) -> Flask:
    """
    Create Flask application with dashboard
    
    Args:
        config: System configuration
        surveillance_system: Reference to surveillance system
        
    Returns:
        Flask application instance
    """
    app = Flask(__name__, 
                template_folder='../../templates',
                static_folder='../../static')
    
    # Configure Flask
    app.config['SECRET_KEY'] = 'traffic_surveillance_secret_key'
    
    # Enable CORS
    CORS(app)
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # Initialize dashboard manager
    dashboard_manager = DashboardManager(config, surveillance_system)
    
    # Routes
    @app.route('/')
    def index():
        """Main dashboard page"""
        return render_template('dashboard.html')
    
    @app.route('/api/dashboard-data')
    def get_dashboard_data():
        """Get comprehensive dashboard data"""
        try:
            data = dashboard_manager.get_dashboard_data()
            return jsonify(data)
        except Exception as e:
            logger.error(f"Error getting dashboard data: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/stats')
    def get_stats():
        """Get current traffic statistics"""
        try:
            stats = dashboard_manager.get_statistics_summary()
            return jsonify(stats)
        except Exception as e:
            logger.error(f"Error getting stats: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/detections')
    def get_detections():
        """Get recent detections"""
        try:
            hours = request.args.get('hours', 1, type=int)
            detections = dashboard_manager.db_manager.get_recent_detections(hours)
            return jsonify(detections)
        except Exception as e:
            logger.error(f"Error getting detections: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/violations')
    def get_violations():
        """Get traffic violations"""
        try:
            hours = request.args.get('hours', 24, type=int)
            violations = dashboard_manager.db_manager.get_violations(hours)
            return jsonify(violations)
        except Exception as e:
            logger.error(f"Error getting violations: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/violation-trends')
    def get_violation_trends():
        """Get violation trends for charts"""
        try:
            hours = request.args.get('hours', 24, type=int)
            trends = dashboard_manager.get_violation_trends(hours)
            return jsonify(trends)
        except Exception as e:
            logger.error(f"Error getting violation trends: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/traffic-flow')
    def get_traffic_flow():
        """Get traffic flow data"""
        try:
            hours = request.args.get('hours', 24, type=int)
            flow_data = dashboard_manager.get_traffic_flow_data(hours)
            return jsonify(flow_data)
        except Exception as e:
            logger.error(f"Error getting traffic flow data: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/system-status')
    def get_system_status():
        """Get system status"""
        try:
            if surveillance_system:
                status = {
                    'running': surveillance_system.running,
                    'stats': surveillance_system.get_system_statistics()
                }
            else:
                status = {
                    'running': False,
                    'stats': {}
                }
            
            return jsonify(status)
        except Exception as e:
            logger.error(f"Error getting system status: {str(e)}")
            return jsonify({'error': str(e)}), 500
    
    # Video streaming endpoint
    @app.route('/video-feed')
    def video_feed():
        """Video streaming route"""
        def generate_frames():
            while True:
                frame_base64 = dashboard_manager.get_current_frame_base64()
                
                if frame_base64:
                    # Extract base64 data
                    frame_data = frame_base64.split(',')[1]
                    frame_bytes = base64.b64decode(frame_data)
                    
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                else:
                    # Send placeholder frame
                    placeholder = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9'
                    
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + placeholder + b'\r\n')
                
                time.sleep(0.1)  # 10 FPS
        
        return Response(generate_frames(),
                       mimetype='multipart/x-mixed-replace; boundary=frame')
    
    # WebSocket events
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        dashboard_manager.connected_clients.add(request.sid)
        logger.info(f"Client connected: {request.sid}")
        
        # Send initial data
        data = dashboard_manager.get_dashboard_data()
        emit('dashboard_update', data)
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        dashboard_manager.connected_clients.discard(request.sid)
        logger.info(f"Client disconnected: {request.sid}")
    
    @socketio.on('request_update')
    def handle_update_request():
        """Handle update request from client"""
        try:
            data = dashboard_manager.get_dashboard_data()
            emit('dashboard_update', data)
        except Exception as e:
            logger.error(f"Error sending update: {str(e)}")
            emit('error', {'message': str(e)})
    
    # Background task to send periodic updates
    def background_updates():
        """Send periodic updates to connected clients"""
        while True:
            if dashboard_manager.connected_clients:
                try:
                    data = dashboard_manager.get_dashboard_data()
                    socketio.emit('dashboard_update', data)
                except Exception as e:
                    logger.error(f"Error in background update: {str(e)}")
            
            time.sleep(2)  # Update every 2 seconds
    
    # Start background update thread
    update_thread = threading.Thread(target=background_updates, daemon=True)
    update_thread.start()
    
    # Store references for external access
    app.dashboard_manager = dashboard_manager
    app.socketio = socketio
    
    return app


# Example usage
if __name__ == "__main__":
    # Test configuration
    config = {
        'database': {
            'type': 'sqlite',
            'path': 'test_dashboard.db'
        },
        'dashboard': {
            'host': '0.0.0.0',
            'port': 5000,
            'debug': True
        }
    }
    
    # Create and run app
    app = create_app(config)
    
    app.run(
        host=config['dashboard']['host'],
        port=config['dashboard']['port'],
        debug=config['dashboard']['debug']
    )
