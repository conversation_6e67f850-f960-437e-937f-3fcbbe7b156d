/* Traffic Surveillance Dashboard Styles */

:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-title {
    color: var(--dark-color);
    font-weight: 600;
}

/* Statistics Cards */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
}

.card.bg-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34) !important;
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800) !important;
}

.card.bg-info {
    background: linear-gradient(135deg, var(--info-color), #138496) !important;
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    background-color: #000;
    border-radius: 0 0 10px 10px;
    overflow: hidden;
}

#video-feed {
    width: 100%;
    height: auto;
    max-height: 500px;
    object-fit: contain;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.overlay-info {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 12px;
    border-radius: 5px;
    color: white;
}

/* Violations List */
.violations-list {
    max-height: 300px;
    overflow-y: auto;
}

.violation-item {
    padding: 10px;
    border-left: 4px solid var(--danger-color);
    background-color: #fff5f5;
    margin-bottom: 10px;
    border-radius: 0 5px 5px 0;
}

.violation-item.speed {
    border-left-color: var(--warning-color);
    background-color: #fffbf0;
}

.violation-item.red-light {
    border-left-color: var(--danger-color);
    background-color: #fff5f5;
}

.violation-type {
    font-weight: bold;
    color: var(--dark-color);
    text-transform: capitalize;
}

.violation-details {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

.violation-time {
    font-size: 0.8rem;
    color: #999;
    float: right;
}

/* System Information */
.system-info {
    font-size: 0.9rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item .label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Status Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.4em 0.8em;
}

.status-online {
    background-color: var(--success-color) !important;
}

.status-offline {
    background-color: var(--danger-color) !important;
}

.status-warning {
    background-color: var(--warning-color) !important;
}

/* Charts */
canvas {
    max-height: 300px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    #video-feed {
        max-height: 300px;
    }
    
    .violations-list {
        max-height: 200px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert Styles */
.alert-violation {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 70px;
    right: 20px;
    z-index: 1000;
    padding: 10px 15px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background-color: var(--success-color);
}

.connection-status.disconnected {
    background-color: var(--danger-color);
}

.connection-status.connecting {
    background-color: var(--warning-color);
}

/* Scrollbar Styling */
.violations-list::-webkit-scrollbar {
    width: 6px;
}

.violations-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.violations-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.violations-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Hover Effects */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.violation-item:hover {
    background-color: #f0f0f0;
    transition: background-color 0.2s ease;
}

/* Traffic Flow Status Colors */
.traffic-light {
    background-color: var(--success-color);
}

.traffic-moderate {
    background-color: var(--warning-color);
}

.traffic-heavy {
    background-color: var(--danger-color);
}

/* Data Update Animation */
.data-updated {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
