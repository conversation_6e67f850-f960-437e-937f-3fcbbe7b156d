// Traffic Surveillance Dashboard JavaScript

class TrafficDashboard {
    constructor() {
        this.socket = null;
        this.charts = {};
        this.connectionStatus = 'disconnected';
        
        this.init();
    }
    
    init() {
        this.initializeSocket();
        this.initializeCharts();
        this.setupEventListeners();
        this.startPeriodicUpdates();
        
        console.log('Traffic Dashboard initialized');
    }
    
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.updateConnectionStatus('connected');
            this.socket.emit('request_update');
        });
        
        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
            this.updateConnectionStatus('disconnected');
        });
        
        this.socket.on('dashboard_update', (data) => {
            this.updateDashboard(data);
        });
        
        this.socket.on('error', (error) => {
            console.error('Socket error:', error);
            this.showAlert('Connection error: ' + error.message, 'danger');
        });
    }
    
    initializeCharts() {
        // Traffic Flow Chart
        const trafficFlowCtx = document.getElementById('traffic-flow-chart').getContext('2d');
        this.charts.trafficFlow = new Chart(trafficFlowCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Vehicle Count',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Average Speed',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Vehicle Count'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Speed (km/h)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
        
        // Violations Chart
        const violationsCtx = document.getElementById('violations-chart').getContext('2d');
        this.charts.violations = new Chart(violationsCtx, {
            type: 'doughnut',
            data: {
                labels: ['Speed Violations', 'Red Light', 'Wrong Way', 'Other'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        '#ffc107',
                        '#dc3545',
                        '#fd7e14',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    setupEventListeners() {
        // Refresh button (if added)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.requestUpdate();
            }
        });
        
        // Video feed error handling
        const videoFeed = document.getElementById('video-feed');
        videoFeed.addEventListener('error', () => {
            console.warn('Video feed error, attempting to reload...');
            setTimeout(() => {
                videoFeed.src = videoFeed.src;
            }, 5000);
        });
    }
    
    startPeriodicUpdates() {
        // Request updates every 5 seconds as backup
        setInterval(() => {
            if (this.connectionStatus === 'connected') {
                this.requestUpdate();
            }
        }, 5000);
        
        // Update timestamp every second
        setInterval(() => {
            this.updateTimestamp();
        }, 1000);
    }
    
    requestUpdate() {
        if (this.socket && this.socket.connected) {
            this.socket.emit('request_update');
        }
    }
    
    updateDashboard(data) {
        try {
            // Update statistics cards
            this.updateStatisticsCards(data.current);
            
            // Update violations list
            this.updateViolationsList(data.recent.violations);
            
            // Update charts
            this.updateCharts(data);
            
            // Update system information
            this.updateSystemInfo(data.current.system_stats);
            
            // Update traffic flow status
            this.updateTrafficFlowStatus(data.current.analytics);
            
        } catch (error) {
            console.error('Error updating dashboard:', error);
        }
    }
    
    updateStatisticsCards(currentData) {
        const analytics = currentData.analytics || {};
        const systemStats = currentData.system_stats || {};
        
        // Vehicle count
        const vehicleCount = analytics.vehicle_count || 0;
        document.getElementById('vehicle-count').textContent = vehicleCount;
        
        // Average speed
        const avgSpeed = analytics.average_speed || 0;
        document.getElementById('avg-speed').textContent = avgSpeed.toFixed(1);
        
        // Violations count
        const violationsCount = analytics.violations_count || 0;
        document.getElementById('violations-count').textContent = violationsCount;
        
        // Processing FPS
        const processingFps = systemStats.processing_fps || 0;
        document.getElementById('processing-fps').textContent = processingFps.toFixed(1);
        
        // Add animation effect
        [vehicleCount, avgSpeed, violationsCount, processingFps].forEach((_, index) => {
            const cards = document.querySelectorAll('.card.bg-primary, .card.bg-success, .card.bg-warning, .card.bg-info');
            if (cards[index]) {
                cards[index].classList.add('data-updated');
                setTimeout(() => cards[index].classList.remove('data-updated'), 500);
            }
        });
    }
    
    updateViolationsList(violations) {
        const violationsList = document.getElementById('violations-list');
        
        if (!violations || violations.length === 0) {
            violationsList.innerHTML = '<p class="text-muted">No recent violations</p>';
            return;
        }
        
        const violationsHtml = violations.slice(0, 10).map(violation => {
            const time = new Date(violation.timestamp * 1000).toLocaleTimeString();
            const type = violation.violation_type.replace('_', ' ');
            
            let details = '';
            if (violation.speed) {
                details = `Speed: ${violation.speed.toFixed(1)} km/h (Limit: ${violation.speed_limit})`;
            }
            
            return `
                <div class="violation-item ${violation.violation_type}">
                    <div class="violation-type">${type}</div>
                    <div class="violation-time">${time}</div>
                    ${details ? `<div class="violation-details">${details}</div>` : ''}
                </div>
            `;
        }).join('');
        
        violationsList.innerHTML = violationsHtml;
    }
    
    updateCharts(data) {
        // Update traffic flow chart
        const analytics = data.current.analytics || {};
        const currentTime = new Date().toLocaleTimeString();
        
        // Add new data point
        this.charts.trafficFlow.data.labels.push(currentTime);
        this.charts.trafficFlow.data.datasets[0].data.push(analytics.vehicle_count || 0);
        this.charts.trafficFlow.data.datasets[1].data.push(analytics.average_speed || 0);
        
        // Keep only last 20 data points
        if (this.charts.trafficFlow.data.labels.length > 20) {
            this.charts.trafficFlow.data.labels.shift();
            this.charts.trafficFlow.data.datasets[0].data.shift();
            this.charts.trafficFlow.data.datasets[1].data.shift();
        }
        
        this.charts.trafficFlow.update('none');
        
        // Update violations chart
        const violations = data.recent.violations || [];
        const violationCounts = this.countViolationsByType(violations);
        
        this.charts.violations.data.datasets[0].data = [
            violationCounts.speed_violation || 0,
            violationCounts.red_light_violation || 0,
            violationCounts.wrong_way_violation || 0,
            violationCounts.other || 0
        ];
        
        this.charts.violations.update('none');
    }
    
    countViolationsByType(violations) {
        const counts = {};
        violations.forEach(violation => {
            const type = violation.violation_type;
            counts[type] = (counts[type] || 0) + 1;
        });
        return counts;
    }
    
    updateSystemInfo(systemStats) {
        if (!systemStats) return;
        
        // Uptime
        const uptime = systemStats.uptime || 0;
        document.getElementById('system-uptime').textContent = this.formatUptime(uptime);
        
        // Frames processed
        const framesProcessed = systemStats.frames_processed || 0;
        document.getElementById('frames-processed').textContent = framesProcessed.toLocaleString();
        
        // Frames dropped
        const framesDropped = systemStats.frames_dropped || 0;
        document.getElementById('frames-dropped').textContent = framesDropped.toLocaleString();
        
        // Total detections
        const totalDetections = systemStats.total_vehicles_detected || 0;
        document.getElementById('total-detections').textContent = totalDetections.toLocaleString();
    }
    
    updateTrafficFlowStatus(analytics) {
        const trafficFlow = analytics?.traffic_flow || {};
        const status = trafficFlow.status || 'Unknown';
        const statusElement = document.getElementById('traffic-flow-status');
        
        // Remove existing classes
        statusElement.classList.remove('traffic-light', 'traffic-moderate', 'traffic-heavy');
        
        // Add appropriate class
        switch (status.toLowerCase()) {
            case 'light':
                statusElement.classList.add('traffic-light');
                break;
            case 'moderate':
                statusElement.classList.add('traffic-moderate');
                break;
            case 'heavy':
                statusElement.classList.add('traffic-heavy');
                break;
        }
        
        statusElement.textContent = `${status} Flow`;
    }
    
    updateConnectionStatus(status) {
        this.connectionStatus = status;
        const statusElement = document.getElementById('system-status');
        
        statusElement.classList.remove('bg-success', 'bg-danger', 'bg-warning');
        
        switch (status) {
            case 'connected':
                statusElement.classList.add('bg-success');
                statusElement.textContent = 'Online';
                break;
            case 'disconnected':
                statusElement.classList.add('bg-danger');
                statusElement.textContent = 'Offline';
                break;
            case 'connecting':
                statusElement.classList.add('bg-warning');
                statusElement.textContent = 'Connecting...';
                break;
        }
    }
    
    updateTimestamp() {
        const timestampElement = document.getElementById('timestamp');
        if (timestampElement) {
            timestampElement.textContent = new Date().toLocaleString();
        }
    }
    
    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
    
    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Add to page
        document.body.insertBefore(alert, document.body.firstChild);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new TrafficDashboard();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.dashboard) {
        window.dashboard.requestUpdate();
    }
});
