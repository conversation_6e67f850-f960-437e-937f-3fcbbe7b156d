#!/usr/bin/env python3
"""
Test Demo - Shows you exactly how to see the output
"""

import cv2
import numpy as np
from pathlib import Path
import json

def create_sample_image():
    """Create a sample image with some rectangles representing vehicles"""
    # Create a sample traffic scene image
    img = np.ones((600, 800, 3), dtype=np.uint8) * 50  # Dark background
    
    # Draw road
    cv2.rectangle(img, (0, 300), (800, 600), (80, 80, 80), -1)
    
    # Draw some "vehicles" as colored rectangles
    vehicles = [
        {"pos": (100, 350), "size": (80, 40), "color": (0, 0, 255), "type": "car"},
        {"pos": (250, 360), "size": (60, 35), "color": (255, 0, 0), "type": "car"},
        {"pos": (400, 340), "size": (120, 60), "color": (0, 255, 0), "type": "truck"},
        {"pos": (600, 370), "size": (50, 30), "color": (255, 255, 0), "type": "motorcycle"},
        {"pos": (150, 450), "size": (70, 35), "color": (255, 0, 255), "type": "car"},
    ]
    
    for vehicle in vehicles:
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        color = vehicle["color"]
        cv2.rectangle(img, (x, y), (x + w, y + h), color, -1)
        cv2.rectangle(img, (x, y), (x + w, y + h), (255, 255, 255), 2)
    
    # Add some text
    cv2.putText(img, "Sample Traffic Scene", (250, 50), 
                cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    
    return img, vehicles

def demonstrate_output():
    """Show all the different ways to see output"""
    
    print("🚀 VEHICLE DETECTION OUTPUT DEMONSTRATION")
    print("="*60)
    
    # Create sample image
    sample_image, vehicles = create_sample_image()
    sample_path = "sample_traffic.jpg"
    cv2.imwrite(sample_path, sample_image)
    
    print(f"✅ Created sample image: {sample_path}")
    
    # Simulate detection results
    results = {
        "image_info": {
            "filename": "sample_traffic.jpg",
            "dimensions": "800x600"
        },
        "detection_summary": {
            "total_vehicles": len(vehicles),
            "vehicle_types_found": len(set(v["type"] for v in vehicles)),
            "license_plates_detected": 2,
            "readable_license_plates": 1
        },
        "vehicle_breakdown": {
            "car": 3,
            "truck": 1,
            "motorcycle": 1
        },
        "license_plates": [
            {"text": "ABC123", "confidence": 0.85}
        ]
    }
    
    print("\n" + "="*60)
    print("📊 OUTPUT METHOD 1: CONSOLE TEXT")
    print("="*60)
    
    print(f"📸 Image: {results['image_info']['filename']}")
    print(f"📏 Size: {results['image_info']['dimensions']}")
    print(f"\n🚗 VEHICLES DETECTED: {results['detection_summary']['total_vehicles']}")
    
    print(f"\n🚙 VEHICLE TYPES:")
    for vehicle_type, count in results['vehicle_breakdown'].items():
        print(f"   {vehicle_type.capitalize()}: {count}")
    
    print(f"\n🔢 LICENSE PLATES:")
    for plate in results['license_plates']:
        print(f"   {plate['text']} (Confidence: {plate['confidence']:.2f})")
    
    print("\n" + "="*60)
    print("📊 OUTPUT METHOD 2: JSON FILE")
    print("="*60)
    
    # Save JSON
    json_file = "detection_results.json"
    with open(json_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"✅ Results saved to: {json_file}")
    print("📄 JSON content preview:")
    print(json.dumps(results, indent=2)[:300] + "...")
    
    print("\n" + "="*60)
    print("📊 OUTPUT METHOD 3: VISUAL IMAGE")
    print("="*60)
    
    # Create annotated image
    annotated_image = sample_image.copy()
    
    # Add detection boxes and labels
    for i, vehicle in enumerate(vehicles):
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        
        # Draw bounding box
        cv2.rectangle(annotated_image, (x-5, y-5), (x + w + 5, y + h + 5), (0, 255, 0), 3)
        
        # Add label
        label = f"{vehicle['type']} {i+1}"
        cv2.putText(annotated_image, label, (x, y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    # Add summary
    cv2.rectangle(annotated_image, (10, 10), (300, 100), (0, 0, 0), -1)
    cv2.putText(annotated_image, f"Vehicles: {len(vehicles)}", (20, 35), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    cv2.putText(annotated_image, f"License Plates: 1", (20, 65), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # Save annotated image
    output_image = "annotated_result.jpg"
    cv2.imwrite(output_image, annotated_image)
    print(f"✅ Annotated image saved to: {output_image}")
    
    print("\n" + "="*60)
    print("📊 OUTPUT METHOD 4: LIVE DISPLAY")
    print("="*60)
    
    print("🖼️  Displaying annotated image...")
    print("   Press ANY KEY to close the window")
    
    # Show the image
    cv2.imshow('Vehicle Detection Results', annotated_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    print("\n" + "="*60)
    print("✅ DEMONSTRATION COMPLETE!")
    print("="*60)
    
    print("\n📋 SUMMARY - You can see output in 4 ways:")
    print("1. 📺 Console text - Printed results in terminal")
    print("2. 📄 JSON file - Structured data saved to file")
    print("3. 🖼️  Annotated image - Visual result with boxes and labels")
    print("4. 🖥️  Live display - Pop-up window showing the result")
    
    print(f"\n📁 Files created in this demo:")
    print(f"   - {sample_path} (sample input image)")
    print(f"   - {output_image} (annotated result image)")
    print(f"   - {json_file} (detection results in JSON)")
    
    return sample_path, output_image, json_file

if __name__ == "__main__":
    demonstrate_output()
