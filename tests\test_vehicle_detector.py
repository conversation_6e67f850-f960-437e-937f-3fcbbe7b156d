"""
Unit tests for Vehicle Detector
"""

import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.models.vehicle_detector import VehicleDetector


class TestVehicleDetector:
    """Test cases for VehicleDetector class"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock YOLO model for testing"""
        with patch('src.models.vehicle_detector.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            yield mock_model
    
    @pytest.fixture
    def detector(self, mock_model):
        """Create VehicleDetector instance with mocked model"""
        return VehicleDetector(model_path="test_model.pt", device="cpu")
    
    @pytest.fixture
    def sample_frame(self):
        """Create a sample frame for testing"""
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    def test_detector_initialization(self, mock_model):
        """Test detector initialization"""
        detector = VehicleDetector(
            model_path="test_model.pt",
            device="cpu",
            confidence_threshold=0.6,
            iou_threshold=0.5
        )
        
        assert detector.model_path == "test_model.pt"
        assert detector.device == "cpu"
        assert detector.confidence_threshold == 0.6
        assert detector.iou_threshold == 0.5
        assert detector.total_detections == 0
        assert detector.frame_count == 0
    
    def test_vehicle_classes(self):
        """Test vehicle class definitions"""
        expected_classes = {2: 'car', 3: 'motorcycle', 5: 'bus', 7: 'truck', 1: 'bicycle'}
        assert VehicleDetector.VEHICLE_CLASSES == expected_classes
    
    def test_detect_vehicles_empty_result(self, detector, sample_frame, mock_model):
        """Test detection with empty results"""
        # Mock empty detection result
        mock_result = Mock()
        mock_result.boxes = None
        mock_model.return_value = [mock_result]
        
        detections = detector.detect_vehicles(sample_frame)
        
        assert detections == []
        assert detector.frame_count == 1
        assert detector.total_detections == 0
    
    def test_detect_vehicles_with_detections(self, detector, sample_frame, mock_model):
        """Test detection with actual detections"""
        # Mock detection result
        mock_box = Mock()
        mock_box.xyxy = [Mock()]
        mock_box.xyxy[0].cpu.return_value.numpy.return_value = [100, 100, 200, 200]
        mock_box.conf = [Mock()]
        mock_box.conf[0].cpu.return_value.numpy.return_value = 0.85
        mock_box.cls = [Mock()]
        mock_box.cls[0].cpu.return_value.numpy.return_value = 2  # car
        
        mock_result = Mock()
        mock_result.boxes = [mock_box]
        mock_model.return_value = [mock_result]
        
        detections = detector.detect_vehicles(sample_frame)
        
        assert len(detections) == 1
        detection = detections[0]
        assert detection['bbox'] == [100, 100, 200, 200]
        assert detection['confidence'] == 0.85
        assert detection['class_id'] == 2
        assert detection['class_name'] == 'car'
        assert detection['center'] == (150, 150)
        assert detection['area'] == 10000
        
        assert detector.frame_count == 1
        assert detector.total_detections == 1
    
    def test_detect_vehicles_filter_non_vehicles(self, detector, sample_frame, mock_model):
        """Test that non-vehicle classes are filtered out"""
        # Mock detection result with person (class 0)
        mock_box = Mock()
        mock_box.xyxy = [Mock()]
        mock_box.xyxy[0].cpu.return_value.numpy.return_value = [100, 100, 200, 200]
        mock_box.conf = [Mock()]
        mock_box.conf[0].cpu.return_value.numpy.return_value = 0.85
        mock_box.cls = [Mock()]
        mock_box.cls[0].cpu.return_value.numpy.return_value = 0  # person (not a vehicle)
        
        mock_result = Mock()
        mock_result.boxes = [mock_box]
        mock_model.return_value = [mock_result]
        
        detections = detector.detect_vehicles(sample_frame)
        
        assert len(detections) == 0
        assert detector.total_detections == 0
    
    def test_draw_detections(self, detector, sample_frame):
        """Test drawing detections on frame"""
        detections = [
            {
                'bbox': [100, 100, 200, 200],
                'confidence': 0.85,
                'class_name': 'car',
                'center': (150, 150)
            }
        ]
        
        result_frame = detector.draw_detections(sample_frame, detections)
        
        # Check that the frame was modified (not equal to original)
        assert not np.array_equal(result_frame, sample_frame)
        assert result_frame.shape == sample_frame.shape
    
    def test_get_statistics(self, detector):
        """Test statistics retrieval"""
        # Simulate some detections
        detector.total_detections = 50
        detector.frame_count = 10
        
        stats = detector.get_statistics()
        
        assert stats['total_detections'] == 50
        assert stats['frames_processed'] == 10
        assert stats['average_detections_per_frame'] == 5.0
        assert stats['model_path'] == detector.model_path
        assert stats['device'] == detector.device
        assert stats['confidence_threshold'] == detector.confidence_threshold
    
    def test_reset_statistics(self, detector):
        """Test statistics reset"""
        # Set some values
        detector.total_detections = 50
        detector.frame_count = 10
        
        detector.reset_statistics()
        
        assert detector.total_detections == 0
        assert detector.frame_count == 0
    
    def test_update_thresholds(self, detector):
        """Test threshold updates"""
        original_conf = detector.confidence_threshold
        original_iou = detector.iou_threshold
        
        detector.update_thresholds(confidence=0.7, iou=0.6)
        
        assert detector.confidence_threshold == 0.7
        assert detector.iou_threshold == 0.6
        
        # Test partial update
        detector.update_thresholds(confidence=0.8)
        assert detector.confidence_threshold == 0.8
        assert detector.iou_threshold == 0.6  # Should remain unchanged
    
    def test_model_loading_error(self):
        """Test handling of model loading errors"""
        with patch('src.models.vehicle_detector.YOLO') as mock_yolo:
            mock_yolo.side_effect = Exception("Model loading failed")
            
            with pytest.raises(Exception):
                VehicleDetector(model_path="nonexistent_model.pt")
    
    def test_detection_error_handling(self, detector, sample_frame, mock_model):
        """Test error handling during detection"""
        # Mock model to raise exception
        mock_model.side_effect = Exception("Detection failed")
        
        detections = detector.detect_vehicles(sample_frame)
        
        # Should return empty list on error
        assert detections == []


if __name__ == "__main__":
    pytest.main([__file__])
