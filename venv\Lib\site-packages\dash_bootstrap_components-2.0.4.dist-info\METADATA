Metadata-Version: 2.4
Name: dash-bootstrap-components
Version: 2.0.4
Summary: Bootstrap themed components for use in Plotly Dash
Project-URL: Homepage, https://www.dash-bootstrap-components.com/
Project-URL: Source, https://github.com/dbc-team/dash-bootstrap-components/
Project-URL: Bug Reports, https://github.com/dbc-team/dash-bootstrap-components/issues
Author: Faculty Science Ltd, Pascal Bugnion
Author-email: <PERSON> <tomc<PERSON><EMAIL>>
Maintainer-email: <PERSON> <<EMAIL>>
License:                               Apache License
                                Version 2.0, January 2004
                            http://www.apache.org/licenses/
        
        TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
        
        1. Definitions.
        
          "License" shall mean the terms and conditions for use, reproduction,
          and distribution as defined by Sections 1 through 9 of this document.
        
          "Licensor" shall mean the copyright owner or entity authorized by
          the copyright owner that is granting the License.
        
          "Legal Entity" shall mean the union of the acting entity and all
          other entities that control, are controlled by, or are under common
          control with that entity. For the purposes of this definition,
          "control" means (i) the power, direct or indirect, to cause the
          direction or management of such entity, whether by contract or
          otherwise, or (ii) ownership of fifty percent (50%) or more of the
          outstanding shares, or (iii) beneficial ownership of such entity.
        
          "You" (or "Your") shall mean an individual or Legal Entity
          exercising permissions granted by this License.
        
          "Source" form shall mean the preferred form for making modifications,
          including but not limited to software source code, documentation
          source, and configuration files.
        
          "Object" form shall mean any form resulting from mechanical
          transformation or translation of a Source form, including but
          not limited to compiled object code, generated documentation,
          and conversions to other media types.
        
          "Work" shall mean the work of authorship, whether in Source or
          Object form, made available under the License, as indicated by a
          copyright notice that is included in or attached to the work
          (an example is provided in the Appendix below).
        
          "Derivative Works" shall mean any work, whether in Source or Object
          form, that is based on (or derived from) the Work and for which the
          editorial revisions, annotations, elaborations, or other modifications
          represent, as a whole, an original work of authorship. For the purposes
          of this License, Derivative Works shall not include works that remain
          separable from, or merely link (or bind by name) to the interfaces of,
          the Work and Derivative Works thereof.
        
          "Contribution" shall mean any work of authorship, including
          the original version of the Work and any modifications or additions
          to that Work or Derivative Works thereof, that is intentionally
          submitted to Licensor for inclusion in the Work by the copyright owner
          or by an individual or Legal Entity authorized to submit on behalf of
          the copyright owner. For the purposes of this definition, "submitted"
          means any form of electronic, verbal, or written communication sent
          to the Licensor or its representatives, including but not limited to
          communication on electronic mailing lists, source code control systems,
          and issue tracking systems that are managed by, or on behalf of, the
          Licensor for the purpose of discussing and improving the Work, but
          excluding communication that is conspicuously marked or otherwise
          designated in writing by the copyright owner as "Not a Contribution."
        
          "Contributor" shall mean Licensor and any individual or Legal Entity
          on behalf of whom a Contribution has been received by Licensor and
          subsequently incorporated within the Work.
        
        2. Grant of Copyright License. Subject to the terms and conditions of
          this License, each Contributor hereby grants to You a perpetual,
          worldwide, non-exclusive, no-charge, royalty-free, irrevocable
          copyright license to reproduce, prepare Derivative Works of,
          publicly display, publicly perform, sublicense, and distribute the
          Work and such Derivative Works in Source or Object form.
        
        3. Grant of Patent License. Subject to the terms and conditions of
          this License, each Contributor hereby grants to You a perpetual,
          worldwide, non-exclusive, no-charge, royalty-free, irrevocable
          (except as stated in this section) patent license to make, have made,
          use, offer to sell, sell, import, and otherwise transfer the Work,
          where such license applies only to those patent claims licensable
          by such Contributor that are necessarily infringed by their
          Contribution(s) alone or by combination of their Contribution(s)
          with the Work to which such Contribution(s) was submitted. If You
          institute patent litigation against any entity (including a
          cross-claim or counterclaim in a lawsuit) alleging that the Work
          or a Contribution incorporated within the Work constitutes direct
          or contributory patent infringement, then any patent licenses
          granted to You under this License for that Work shall terminate
          as of the date such litigation is filed.
        
        4. Redistribution. You may reproduce and distribute copies of the
          Work or Derivative Works thereof in any medium, with or without
          modifications, and in Source or Object form, provided that You
          meet the following conditions:
        
          (a) You must give any other recipients of the Work or
              Derivative Works a copy of this License; and
        
          (b) You must cause any modified files to carry prominent notices
              stating that You changed the files; and
        
          (c) You must retain, in the Source form of any Derivative Works
              that You distribute, all copyright, patent, trademark, and
              attribution notices from the Source form of the Work,
              excluding those notices that do not pertain to any part of
              the Derivative Works; and
        
          (d) If the Work includes a "NOTICE" text file as part of its
              distribution, then any Derivative Works that You distribute must
              include a readable copy of the attribution notices contained
              within such NOTICE file, excluding those notices that do not
              pertain to any part of the Derivative Works, in at least one
              of the following places: within a NOTICE text file distributed
              as part of the Derivative Works; within the Source form or
              documentation, if provided along with the Derivative Works; or,
              within a display generated by the Derivative Works, if and
              wherever such third-party notices normally appear. The contents
              of the NOTICE file are for informational purposes only and
              do not modify the License. You may add Your own attribution
              notices within Derivative Works that You distribute, alongside
              or as an addendum to the NOTICE text from the Work, provided
              that such additional attribution notices cannot be construed
              as modifying the License.
        
          You may add Your own copyright statement to Your modifications and
          may provide additional or different license terms and conditions
          for use, reproduction, or distribution of Your modifications, or
          for any such Derivative Works as a whole, provided Your use,
          reproduction, and distribution of the Work otherwise complies with
          the conditions stated in this License.
        
        5. Submission of Contributions. Unless You explicitly state otherwise,
          any Contribution intentionally submitted for inclusion in the Work
          by You to the Licensor shall be under the terms and conditions of
          this License, without any additional terms or conditions.
          Notwithstanding the above, nothing herein shall supersede or modify
          the terms of any separate license agreement you may have executed
          with Licensor regarding such Contributions.
        
        6. Trademarks. This License does not grant permission to use the trade
          names, trademarks, service marks, or product names of the Licensor,
          except as required for reasonable and customary use in describing the
          origin of the Work and reproducing the content of the NOTICE file.
        
        7. Disclaimer of Warranty. Unless required by applicable law or
          agreed to in writing, Licensor provides the Work (and each
          Contributor provides its Contributions) on an "AS IS" BASIS,
          WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
          implied, including, without limitation, any warranties or conditions
          of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
          PARTICULAR PURPOSE. You are solely responsible for determining the
          appropriateness of using or redistributing the Work and assume any
          risks associated with Your exercise of permissions under this License.
        
        8. Limitation of Liability. In no event and under no legal theory,
          whether in tort (including negligence), contract, or otherwise,
          unless required by applicable law (such as deliberate and grossly
          negligent acts) or agreed to in writing, shall any Contributor be
          liable to You for damages, including any direct, indirect, special,
          incidental, or consequential damages of any character arising as a
          result of this License or out of the use or inability to use the
          Work (including but not limited to damages for loss of goodwill,
          work stoppage, computer failure or malfunction, or any and all
          other commercial damages or losses), even if such Contributor
          has been advised of the possibility of such damages.
        
        9. Accepting Warranty or Additional Liability. While redistributing
          the Work or Derivative Works thereof, You may choose to offer,
          and charge a fee for, acceptance of support, warranty, indemnity,
          or other liability obligations and/or rights consistent with this
          License. However, in accepting such obligations, You may act only
          on Your own behalf and on Your sole responsibility, not on behalf
          of any other Contributor, and only if You agree to indemnify,
          defend, and hold each Contributor harmless for any liability
          incurred by, or claims asserted against, such Contributor by reason
          of your accepting any such warranty or additional liability.
        
        END OF TERMS AND CONDITIONS
        
        APPENDIX: How to apply the Apache License to your work.
        
          To apply the Apache License to your work, attach the following
          boilerplate notice, with the fields enclosed by brackets "[]"
          replaced with your own identifying information. (Don't include
          the brackets!)  The text should be enclosed in the appropriate
          comment syntax for the file format. We also recommend that a
          file or class name and description of purpose be included on the
          same "printed page" as the copyright notice for easier
          identification within third-party archives.
        
        Copyright 2018-2025 Faculty Science Limited
        Copyright 2025 the dash-bootstrap-components maintainers
        
        Licensed under the Apache License, Version 2.0 (the "License");
        you may not use this file except in compliance with the License.
        You may obtain a copy of the License at
        
            http://www.apache.org/licenses/LICENSE-2.0
        
        Unless required by applicable law or agreed to in writing, software
        distributed under the License is distributed on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        See the License for the specific language governing permissions and
        limitations under the License.
License-File: LICENSE
License-File: NOTICE.txt
Classifier: Framework :: Dash
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Requires-Dist: dash>=3.0.4
Provides-Extra: pandas
Requires-Dist: numpy>=2.0.2; extra == 'pandas'
Requires-Dist: pandas>=2.2.3; extra == 'pandas'
Description-Content-Type: text/markdown

<p align="center">
  <a href="https://www.dash-bootstrap-components.com/">
    <img src="https://cdn.jsdelivr.net/gh/dbc-team/dash-bootstrap-components@main/readme-images/logo.png" alt="dash-bootstrap-components logo" width="200" height="200">
  </a>
</p>

<h3 align="center">Dash Bootstrap Components</h3>

<p align="center">
  Bootstrap components for Plotly Dash
  <br>
  <a href="https://www.dash-bootstrap-components.com/">Explore the documentation</a>
  ·
  <a href="https://github.com/dbc-team/dash-bootstrap-components/issues/new?template=bug.md">Report a bug</a>
  ·
  <a href="https://github.com/dbc-team/dash-bootstrap-components/issues/new?template=feature.md">Request a feature</a>
  <br>
  <br>
  <img alt="GitHub Actions" src="https://github.com/dbc-team/dash-bootstrap-components/actions/workflows/tests.yml/badge.svg">
  <img alt="GitHub" src="https://img.shields.io/github/license/dbc-team/dash-bootstrap-components">
  <img alt="PyPI" src="https://img.shields.io/pypi/v/dash-bootstrap-components">
  <img alt="Conda (channel only)" src="https://img.shields.io/conda/vn/conda-forge/dash-bootstrap-components">
  <img alt="PyPI - Python Version" src="https://img.shields.io/pypi/pyversions/dash-bootstrap-components">
</p>

_dash-bootstrap-components_ is a library of [Bootstrap][bootstrap-homepage]
components for use with [Plotly Dash][dash-homepage], that makes it easier to
build consistently styled Dash apps with complex, responsive layouts.

## Table of contents

- [Installation](#installation)
- [Quick start](#quick-start)
- [Contributing](#contributing)
- [Copyright and license](#copyright-and-license)

## Installation

### PyPI

You can install _dash-bootstrap-components_ with `pip`:

```sh
pip install dash-bootstrap-components
```

### Anaconda

You can also install _dash-bootstrap-components_ with `conda` through the
conda-forge channel:

```sh
conda install -c conda-forge dash-bootstrap-components
```

## Quick start

To use _dash-bootstrap-components_ you must do two things:

- Link a Bootstrap v5 compatible stylesheet
- Incorporate _dash-bootstrap-components_ into your layout

### Linking a stylesheet

_dash-bootstrap-components_ doesn't come with CSS included. This is to give you
the freedom to use any Bootstrap v5 stylesheet of your choice. This means
however that in order for the components to be styled properly, you must link
to a stylesheet yourself.

For convenience, links to [BootstrapCDN][bootstrapcdn] for each theme are
available through the `themes` module, which can be used as follows:

```python
import dash
import dash_bootstrap_components as dbc

app = dash.Dash(external_stylesheets=[dbc.themes.BOOTSTRAP])
```

For more information on how to link local or external CSS, check out the
[Dash documentation][dash-docs-external].

### Build the layout

With CSS linked, you can start building your app's layout with our Bootstrap
components. These include layout components for organising the content of your app on the page, as well as UI components like navbars, cards, alerts and many more.

![layout](https://cdn.jsdelivr.net/gh/dbc-team/dash-bootstrap-components@main/readme-images/layout.png)

See our [_documentation_][docs-components] for a full list of available
components.

## Contributing

We welcome contributions to _dash-bootstrap-components_. If you find a bug or
something is unclear please [submit a bug report][bug-report], if you have ideas
for new features please feel free to make a [feature request][feature-request].

If you would like to submit a pull request, please read our
[contributing guide][contribution-guide], which contains instructions on how to
build and install _dash-bootstrap-components_ locally, how to check your code
will pass our linting checks, and how to submit the pull request itself.

## Acknowledgements

The _dash-bootstrap-components_ maintainers would like to thank [Faculty][faculty] for their early support in helping this project get off the ground. Since 2025, it has been maintained by the Dash community.

## Copyright and license

Copyright © 2018–2025 [Faculty Science Ltd.][faculty]
Copyright © 2025 the _dash-bootstrap-components_ maintainers

Released under the [Apache 2.0 license](https://github.com/dbc-team/dash-bootstrap-components/blob/main/LICENSE).


[dash-homepage]: https://dash.plotly.com/
[dash-docs-external]: https://dash.plotly.com/external-resources
[bootstrap-homepage]: https://getbootstrap.com/
[docs-components]: https://www.dash-bootstrap-components.com/docs/components
[bootstrapcdn]: https://www.bootstrapcdn.com/
[faculty]: https://faculty.ai
[bug-report]: https://github.com/dbc-team/dash-bootstrap-components/issues/new?template=bug.md
[feature-request]: https://github.com/dbc-team/dash-bootstrap-components/issues/new?template=feature.md
[contribution-guide]: https://github.com/dbc-team/dash-bootstrap-components/blob/main/.github/CONTRIBUTING.md
