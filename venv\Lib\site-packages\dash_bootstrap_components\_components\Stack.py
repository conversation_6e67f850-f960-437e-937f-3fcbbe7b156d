# AUTO GENERATED FILE - DO NOT EDIT

import typing  # noqa: F401
from typing_extensions import TypedDict, NotRequired, Literal # noqa: F401
from dash.development.base_component import Component, _explicitize_args

ComponentType = typing.Union[
    str,
    int,
    float,
    Component,
    None,
    typing.Sequence[typing.Union[str, int, float, Component, None]],
]

NumberType = typing.Union[
    typing.SupportsFloat, typing.SupportsInt, typing.SupportsComplex
]


class Stack(Component):
    """A Stack component.
Stacks are shorthand helpers that build on top of existing flexbox
utilities to make component layout faster and easier than ever.

Keyword arguments:

- children (a list of or a singular dash component, string or number; optional):
    The children of this Stack.

- id (string; optional):
    The ID of the Stack.

- direction (a value equal to: 'vertical', 'horizontal'; optional):
    Which direction to stack the objects in.

- gap (number; optional):
    Set the spacing between each item (0 - 5).

- class_name (string; optional):
    Additional CSS classes to apply to the Stack.

- key (string; optional):
    A unique identifier for the component, used to improve performance
    by React.js while rendering components  See
    https://react.dev/learn/rendering-lists#why-does-react-need-keys
    for more info.

- className (string; optional):
    **DEPRECATED** Use `class_name` instead.  Additional CSS classes
    to apply to the Stack."""
    _children_props = []
    _base_nodes = ['children']
    _namespace = 'dash_bootstrap_components'
    _type = 'Stack'


    def __init__(
        self,
        children: typing.Optional[ComponentType] = None,
        id: typing.Optional[typing.Union[str, dict]] = None,
        *,
        direction: typing.Optional[Literal["vertical", "horizontal"]] = None,
        gap: typing.Optional[NumberType] = None,
        style: typing.Optional[typing.Any] = None,
        class_name: typing.Optional[str] = None,
        key: typing.Optional[str] = None,
        className: typing.Optional[str] = None,
        **kwargs
    ):
        self._prop_names = ['children', 'id', 'direction', 'gap', 'style', 'class_name', 'key', 'className']
        self._valid_wildcard_attributes =            []
        self.available_properties = ['children', 'id', 'direction', 'gap', 'style', 'class_name', 'key', 'className']
        self.available_wildcard_properties =            []
        _explicit_args = kwargs.pop('_explicit_args')
        _locals = locals()
        _locals.update(kwargs)  # For wildcard attrs and excess named props
        args = {k: _locals[k] for k in _explicit_args if k != 'children'}

        super(Stack, self).__init__(children=children, **args)

setattr(Stack, "__init__", _explicitize_args(Stack.__init__))
