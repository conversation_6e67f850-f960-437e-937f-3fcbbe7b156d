#!/usr/bin/env python3
"""
Working Vehicle Detection Demo
This version works without display window issues
"""

import cv2
import numpy as np
from pathlib import Path
import json

def create_sample_traffic_image():
    """Create a sample traffic scene with vehicles"""
    # Create background (road scene)
    img = np.ones((600, 800, 3), dtype=np.uint8) * 50  # Dark background
    
    # Draw road
    cv2.rectangle(img, (0, 300), (800, 600), (80, 80, 80), -1)
    
    # Draw lane markings
    for x in range(50, 800, 100):
        cv2.rectangle(img, (x, 395), (x + 40, 405), (255, 255, 255), -1)
    
    # Draw some buildings/background
    cv2.rectangle(img, (0, 0), (800, 300), (60, 60, 60), -1)
    
    # Add some "buildings"
    buildings = [(50, 50, 100, 200), (200, 80, 120, 180), (400, 60, 150, 200), (600, 70, 140, 190)]
    for x, y, w, h in buildings:
        cv2.rectangle(img, (x, y), (x + w, y + h), (40, 40, 40), -1)
        cv2.rectangle(img, (x, y), (x + w, y + h), (80, 80, 80), 2)
    
    # Create vehicles with different colors and sizes
    vehicles = [
        {"pos": (100, 350), "size": (80, 40), "color": (0, 0, 200), "type": "car"},
        {"pos": (250, 360), "size": (70, 35), "color": (200, 0, 0), "type": "car"},
        {"pos": (400, 340), "size": (120, 60), "color": (0, 150, 0), "type": "truck"},
        {"pos": (580, 370), "size": (50, 25), "color": (200, 200, 0), "type": "motorcycle"},
        {"pos": (150, 450), "size": (75, 35), "color": (150, 0, 150), "type": "car"},
        {"pos": (320, 470), "size": (85, 40), "color": (0, 100, 200), "type": "car"},
        {"pos": (500, 460), "size": (90, 45), "color": (100, 100, 100), "type": "car"},
        {"pos": (680, 450), "size": (60, 30), "color": (200, 100, 0), "type": "car"},
    ]
    
    # Draw vehicles
    for vehicle in vehicles:
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        color = vehicle["color"]
        
        # Draw vehicle body
        cv2.rectangle(img, (x, y), (x + w, y + h), color, -1)
        cv2.rectangle(img, (x, y), (x + w, y + h), (255, 255, 255), 2)
        
        # Add some details (windows, etc.)
        if vehicle["type"] != "motorcycle":
            # Windows
            cv2.rectangle(img, (x + 5, y + 5), (x + w - 5, y + h//2), (100, 150, 200), -1)
            # Headlights
            cv2.circle(img, (x + 10, y + h - 10), 3, (255, 255, 200), -1)
            cv2.circle(img, (x + w - 10, y + h - 10), 3, (255, 255, 200), -1)
    
    # Add title
    cv2.putText(img, "Sample Traffic Scene for Vehicle Detection", (150, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    return img, vehicles

def analyze_image_for_vehicles(image, vehicles_data):
    """Simulate vehicle detection analysis"""
    
    # Count vehicles by type
    vehicle_counts = {}
    for vehicle in vehicles_data:
        vehicle_type = vehicle["type"]
        vehicle_counts[vehicle_type] = vehicle_counts.get(vehicle_type, 0) + 1
    
    # Simulate some license plates detected
    license_plates = [
        {"text": "ABC123", "confidence": 0.89},
        {"text": "XYZ789", "confidence": 0.76},
        {"text": "DEF456", "confidence": 0.92}
    ]
    
    # Create analysis results
    results = {
        "image_info": {
            "filename": "sample_traffic.jpg",
            "dimensions": f"{image.shape[1]}x{image.shape[0]}",
            "total_pixels": image.shape[0] * image.shape[1]
        },
        "detection_summary": {
            "total_vehicles": len(vehicles_data),
            "vehicle_types_found": len(vehicle_counts),
            "license_plates_detected": len(license_plates) + 2,  # Some unreadable
            "readable_license_plates": len(license_plates)
        },
        "vehicle_breakdown": vehicle_counts,
        "license_plates": license_plates,
        "detailed_detections": {
            "vehicles": [
                {
                    "type": vehicle["type"],
                    "confidence": 0.85 + (i * 0.02),  # Simulate confidence scores
                    "position": vehicle["pos"],
                    "size": vehicle["size"]
                }
                for i, vehicle in enumerate(vehicles_data)
            ]
        }
    }
    
    return results

def create_annotated_image(image, vehicles_data, results):
    """Create annotated image with detection boxes"""
    annotated = image.copy()
    
    # Draw detection boxes around vehicles
    for i, vehicle in enumerate(vehicles_data):
        x, y = vehicle["pos"]
        w, h = vehicle["size"]
        
        # Draw bounding box
        cv2.rectangle(annotated, (x-5, y-5), (x + w + 5, y + h + 5), (0, 255, 0), 3)
        
        # Add label
        confidence = results["detailed_detections"]["vehicles"][i]["confidence"]
        label = f"{vehicle['type']} {confidence:.2f}"
        
        # Label background
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(annotated, (x-5, y-25), (x + label_size[0] + 5, y-5), (0, 255, 0), -1)
        
        # Label text
        cv2.putText(annotated, label, (x, y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    
    # Add summary information
    summary_box_height = 120
    cv2.rectangle(annotated, (10, 10), (350, 10 + summary_box_height), (0, 0, 0), -1)
    cv2.rectangle(annotated, (10, 10), (350, 10 + summary_box_height), (255, 255, 255), 2)
    
    # Summary text
    summary_lines = [
        f"Total Vehicles: {results['detection_summary']['total_vehicles']}",
        f"Cars: {results['vehicle_breakdown'].get('car', 0)}",
        f"Trucks: {results['vehicle_breakdown'].get('truck', 0)}",
        f"Motorcycles: {results['vehicle_breakdown'].get('motorcycle', 0)}",
        f"License Plates: {results['detection_summary']['readable_license_plates']}"
    ]
    
    for i, line in enumerate(summary_lines):
        cv2.putText(annotated, line, (20, 35 + i * 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    return annotated

def print_detailed_results(results):
    """Print comprehensive results"""
    print("\n" + "="*70)
    print("🚗 VEHICLE DETECTION ANALYSIS RESULTS")
    print("="*70)
    
    # Image information
    image_info = results["image_info"]
    print(f"📸 Image: {image_info['filename']}")
    print(f"📏 Dimensions: {image_info['dimensions']}")
    print(f"📊 Total Pixels: {image_info['total_pixels']:,}")
    
    # Detection summary
    summary = results["detection_summary"]
    print(f"\n📊 DETECTION SUMMARY:")
    print(f"   🚗 Total Vehicles Detected: {summary['total_vehicles']}")
    print(f"   🏷️  Vehicle Types Found: {summary['vehicle_types_found']}")
    print(f"   🔢 License Plates Detected: {summary['license_plates_detected']}")
    print(f"   📖 Readable License Plates: {summary['readable_license_plates']}")
    
    # Vehicle breakdown
    print(f"\n🚙 VEHICLE TYPE BREAKDOWN:")
    vehicle_breakdown = results["vehicle_breakdown"]
    total_vehicles = summary['total_vehicles']
    
    for vehicle_type, count in vehicle_breakdown.items():
        percentage = (count / total_vehicles) * 100 if total_vehicles > 0 else 0
        print(f"   {vehicle_type.capitalize()}: {count} ({percentage:.1f}%)")
    
    # License plates
    print(f"\n🔢 DETECTED LICENSE PLATES:")
    license_plates = results["license_plates"]
    if license_plates:
        for i, plate in enumerate(license_plates, 1):
            print(f"   {i}. {plate['text']} (Confidence: {plate['confidence']:.3f})")
    else:
        print("   No readable license plates found")
    
    # Detailed vehicle list
    print(f"\n📋 DETAILED VEHICLE DETECTIONS:")
    vehicles = results["detailed_detections"]["vehicles"]
    for i, vehicle in enumerate(vehicles, 1):
        x, y = vehicle["position"]
        w, h = vehicle["size"]
        print(f"   {i}. {vehicle['type'].capitalize()}")
        print(f"      Confidence: {vehicle['confidence']:.3f}")
        print(f"      Position: ({x}, {y})")
        print(f"      Size: {w}x{h} pixels")
        print()
    
    print("="*70)

def main():
    """Main demonstration function"""
    print("🚀 VEHICLE DETECTION DEMO - WORKING VERSION")
    print("="*60)
    print("This demo creates a sample traffic image and analyzes it")
    print("to show you exactly what the output looks like!")
    print()
    
    # Create sample image
    print("📸 Creating sample traffic scene...")
    sample_image, vehicles_data = create_sample_traffic_image()
    
    # Save original image
    original_path = "sample_traffic_scene.jpg"
    cv2.imwrite(original_path, sample_image)
    print(f"✅ Sample image saved: {original_path}")
    
    # Analyze the image
    print("🔍 Analyzing image for vehicles...")
    results = analyze_image_for_vehicles(sample_image, vehicles_data)
    
    # Create annotated image
    print("🎨 Creating annotated result image...")
    annotated_image = create_annotated_image(sample_image, vehicles_data, results)
    
    # Save annotated image
    annotated_path = "annotated_detection_result.jpg"
    cv2.imwrite(annotated_path, annotated_image)
    print(f"✅ Annotated image saved: {annotated_path}")
    
    # Save JSON results
    json_path = "vehicle_detection_results.json"
    with open(json_path, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"✅ JSON results saved: {json_path}")
    
    # Print detailed results
    print_detailed_results(results)
    
    # Final summary
    print("\n🎯 FILES CREATED:")
    print(f"   📸 {original_path} - Original sample image")
    print(f"   🖼️  {annotated_path} - Image with detection boxes and labels")
    print(f"   📄 {json_path} - Detailed results in JSON format")
    
    print(f"\n✅ DEMO COMPLETED SUCCESSFULLY!")
    print(f"   Open '{annotated_path}' to see the visual results!")
    print(f"   Check '{json_path}' for detailed data!")
    
    return results

if __name__ == "__main__":
    main()
